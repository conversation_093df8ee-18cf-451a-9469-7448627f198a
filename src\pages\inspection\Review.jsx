import React, { Fragment, useEffect, useRef, useState } from 'react';
import {
  conveyorControllerWidth,
  conveyorOperation,
  filletLowerThreshold,
  filletOpenThreshold,
  filletUpperThreshold,
  filletVolumeRatio,
  localStorageKeys,
  serverHost,
  leadInspection2D,
  solderInspection2D,
  solderValidRatioList,
  padValidRatioList,
  tipValidRatioList,
  validRatioList,
  solder2DValidRatioRanges,
  solderValidRatioRange,
  liftedLeadTipValidRatioRange,
} from '../../common/const';
import { Button, Collapse, ConfigProvider, Input, InputNumber, Tooltip, Tabs } from 'antd';
import { CustomCollapse, CustomMenu } from '../../common/styledComponent';
import { useLazyGetAllConveyorStatusQuery, useSubmitConveyorOperationMutation } from '../../services/conveyor';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAnnotateFeatureMutation, useAnnotateGroupMutation, useCancelFeedbackMutation, useCreateComponentVariationMutation, useGetAllInspectionsQuery, useGetInspectedComponentQuery, useGetInspectedFeatureQuery, useGetNeighborInspectionsQuery, useGetSessionInfoQuery, useLazyExportInspectedFeatureQuery, useLazyGetInferenceStatusQuery, useLazyGetLineItemResultsQuery, useSubmitGoodFeedbackForAllMutation } from '../../services/inference';
import { useGetAllFeaturesQuery, useGetProductByIdQuery, useGetProductComponentQuery, useLazyGetFeatureByFeatureIdQuery, useScanBarcodeMutation, useUpdateProductMutation } from '../../services/product';
import InspectionReviewDisplay from './InspectionReviewDisplay';
import { getAgentParamTypeNValueByParam, getCurrentConveyorStatus } from '../../common/util';
import { useDispatch, useSelector } from 'react-redux';
import { setIsContainerLvlLoadingEnabled } from '../../reducer/setting';
import { systemApi } from '../../services/system';
import InferenceResult from '../../components/InferenceResult';
import CommonTable from '../../components/CommonTable';


const Review = () => {
  const [searchParams] = useSearchParams();
  const ipcProductId = searchParams.get('ipc-product-id');
  const goldenProductId = searchParams.get('golden-product-id');
  const ipcSessionId = searchParams.get('ipc-session-id');
  const isFromLive = searchParams.get('is-from-live') === 'true';
  const slotId = searchParams.get('slot-id');
  const queryString = searchParams.get('query');
  let worklistQuery = {};
  try {
    if (queryString) worklistQuery = JSON.parse(queryString);
  } catch (e) {
    worklistQuery = {};
  }
  const pageInitialized = useRef(false); // to avoid first render issue, we need to wait for the data to be fetched before rendering the page

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const navigate = useNavigate();

  const [featureList, setFeatureList] = useState([]);
  const [selectedRCid, setSelectedRCid] = useState(null); // selected result component id
  const [selectedDCid, setSelectedDCid] = useState(null); // selected definition component id
  const [selectedFid, setSelectedFid] = useState(null); // feature ids are matched within the same component in result and definition
  const [selectedDetail, setSelectedDetail] = useState(null); // selected line item name
  const [parsedComponents, setParsedComponents] = useState(null);
  const [selectedErrorCategory, setSelectedErrorCategory] = useState(null);
  const [selectedInspectionResultId, setSelectedInspectionResultId] = useState(null);
  const [selectedResultComponent, setSelectedResultComponent] = useState(null);
  const [selectedArrayIndex, setSelectedArrayIndex] = useState(null);
  const [maskInfo, setMaskInfo] = useState({ masks: [], selectedIndex: 0, show: false });
  const [activeTab, setActiveTab] = useState('review');
  const [subUnitInfo, setSubUnitInfo] = useState([]);

  const { data: goldenFeatures } = useGetAllFeaturesQuery({ product_id: Number(goldenProductId), step: 0, marker: false });
  const { data: goldenComponents } = useGetProductComponentQuery({ definition_product_id: Number(goldenProductId), definition_step: 0 });
  const { data: inspectedComponents, refetch: refetchInspectedComponents } = useGetInspectedComponentQuery({ inspected_product_id: ipcProductId, step: 0, passing: false });
  const { data: inspectedFeatures, refetch: refetchInspectedFeatures } = useGetInspectedFeatureQuery({ inspected_product_id: ipcProductId, step: 0, passing: false });
  const { data: inspectedProduct, refetch: refetchIpcProduct } = useGetProductByIdQuery(ipcProductId);
  const { data: goldenProduct } = useGetProductByIdQuery(goldenProductId);
  const [submitConveyorOperation] = useSubmitConveyorOperationMutation();
  const [annotate] = useAnnotateFeatureMutation();
  const [cancelAnnotation] = useCancelFeedbackMutation();
  const [submitGoodFeedbackForAll] = useSubmitGoodFeedbackForAllMutation();
  const {
    data: neighborInspections,
    refetch: refetchNeighborInspections,
    isLoading: isLoadingNeighborInspections,
    isFetching: isFetchingNeighborInspections,
    isError: isErrorNeighborInspections,
  } = useGetNeighborInspectionsQuery({
    product_id: ipcProductId,
    ipc_session_id: ipcSessionId,
    ...worklistQuery,
  });
  const { data: sessionInfo, refetch: refetchSessionInfo } = useGetSessionInfoQuery(ipcSessionId);

  const allInspectionCountReq = useGetAllInspectionsQuery({
    ipc_session_id: ipcSessionId,
    ...worklistQuery,
    limit: 1,
  });

  const [lazyGetConveyorStatus] = useLazyGetAllConveyorStatusQuery();
  const [lazyGetInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [exportInspectedFeature] = useLazyExportInspectedFeatureQuery();
  const [annotateGroup] = useAnnotateGroupMutation();
  const [scanBarcode] = useScanBarcodeMutation();
  const [updateProduct] = useUpdateProductMutation();
  const [createComponentVariation] = useCreateComponentVariationMutation();

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  // console.log('selectedFid', selectedFid);
  // console.log('selectedRCid', selectedRCid);
  // console.log('selectedDCid', selectedDCid);
  // console.log('selectedDetail', selectedDetail);
  // console.log('selectedErrorCategory', selectedErrorCategory);

  const firstItemInitiallized = useRef(false);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.shiftKey) {
        return;
      }

      const activeElement = document.activeElement;
      if (activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.contentEditable === 'true'
      )) {
        return;
      }

      if (!selectedRCid || !systemMetadata) {
        return;
      }

      const errorTypes = _.keys(_.get(systemMetadata, 'error_type_to_code', {}));
      if (_.isEmpty(errorTypes)) {
        return;
      }

      let keyIndex = -1;
      const key = event.key;

      if (key >= '1' && key <= '9') {
        const numKey = parseInt(key);
        if (event.ctrlKey && event.altKey) {
          // Ctrl+Alt+1-9
          keyIndex = numKey + 18 - 1;
        } else if (event.ctrlKey) {
          // Ctrl+1-9
          keyIndex = numKey + 9 - 1;
        } else if (event.altKey) {
          // Alt+1-9
          keyIndex = numKey + 27 - 1;
        } else {
          // 1-9
          keyIndex = numKey - 1;
        }
      }

      if (keyIndex >= 0 && keyIndex < errorTypes.length) {
        const errorType = errorTypes[keyIndex];

        event.preventDefault();

        const run = async (selectedRCid, errorType, ipcProductId, arrayIndex) => {
          const res = await annotateGroup({
            product_id: Number(ipcProductId),
            group_id: Number(selectedRCid),
            step: 0,
            error_type: errorType,
            array_index: arrayIndex,
          });

          if (res.error) {
            aoiAlert(t('notification.error.annotateGroup'), ALERT_TYPES.COMMON_ERROR);
            console.error('annotateGroup error:', _.get(res, 'error.message', ''));
            return;
          }

          await refetchInspectedComponents();
          await refetchInspectedFeatures();
        };

        // run(selectedRCid, errorType, ipcProductId, _.get(parsedComponents, `[${selectedRCid}].array_index`, null));
        run(selectedRCid, errorType, ipcProductId, selectedArrayIndex);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedRCid, systemMetadata, parsedComponents, ipcProductId, annotateGroup, refetchInspectedComponents, refetchInspectedFeatures, t]);

  const getShortcutText = (index) => {
    if (index < 9) {
      return `(${index + 1})`;
    } else if (index < 18) {
      return `(Ctrl+${index - 8})`;
    } else if (index < 27) {
      return `(Alt+${index - 17})`;
    } else {
      return `(Ctrl+Alt+${index - 26})`;
    }
  };

  const handleCreateComponentVariantion = async (
    selectedRCid,
    ipcProductId,
    step,
    predictedErrorType,
    arrayIndex,
  ) => {
    const res = await createComponentVariation({
      array_index: arrayIndex,
      product_id: Number(ipcProductId),
      step,
      group_id: Number(selectedRCid),
      error_type: predictedErrorType,
    });

    if (res.error) {
      aoiAlert(t('notification.error.createComponentVariation'), ALERT_TYPES.COMMON_ERROR);
      console.error('createComponentVariation error:', _.get(res, 'error.message', ''));
      return;
    }

    aoiAlert(t('notification.success.createComponentVariation'), ALERT_TYPES.COMMON_SUCCESS);
  };

  const handleFeatureFeedbackSubmit = async (inspectedFeatures, selectedFid, selectedRCid, isGood, ipcProductId, selectedDetail) => {
    const inspectedfeatureObj = _.find(inspectedFeatures, f => f.feature_id === selectedFid && f.component_id === selectedRCid && f.detail === selectedDetail);

    if (!inspectedfeatureObj) return;

    if (
      !_.isBoolean(_.get(inspectedfeatureObj, 'feedback.correct')) ||
      (
        _.get(inspectedfeatureObj, 'feedback.correct') === true &&
        _.get(inspectedfeatureObj, 'pass') !== isGood
      ) ||
      (
        _.get(inspectedfeatureObj, 'feedback.correct') === false &&
        _.get(inspectedfeatureObj, 'pass') === isGood
      )
    ) {
      const res = await annotate({
        // variant: _.get(selectedFeature, 'variant'),
        product_id: Number(ipcProductId),
        step: 0,
        feature_id: _.get(inspectedfeatureObj, 'feature_id'),
        line_item_name: _.get(inspectedfeatureObj, 'detail'),
        is_inference_correct: _.get(inspectedfeatureObj, 'pass') === isGood,
      });

      if (res.error) {
        aoiAlert(t('notification.error.provideFeedback'), ALERT_TYPES.COMMON_ERROR);
        console.error('annotate error:', _.get(res, 'error.message', ''));
        return;
      }
    } else {
      // else cancel feedback
      const res = await cancelAnnotation({
        ...inspectedfeatureObj,
      });

      if (res.error) {
        aoiAlert(t('notification.error.cancelFeedback'), ALERT_TYPES.COMMON_ERROR);
        console.error('cancelFeedback error:', _.get(res, 'error.message', ''));
        return;
      }
    }

    const refetchRes = await refetchInspectedFeatures();

    if (refetchRes.error) {
      aoiAlert(t('notification.error.refetchInspectedFeatures'), ALERT_TYPES.COMMON_ERROR);
      console.error('refetchInspectedFeatures error:', _.get(refetchRes, 'error.message', ''));
      return;
    }
  };

  const handleComponentPassClick = async (parsedComponents, selectedRCid, ipcProductId) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));

    // we need the fids and the feature agent name(detail) and is inference correct
    const componentErrorsObj = _.get(parsedComponents, `${selectedRCid}.errors`, {});
    const target = [];

    for (const errorCategory of _.keys(componentErrorsObj)) {
      for (const featureId of _.keys(componentErrorsObj[errorCategory])) {
        const featureObj = _.get(componentErrorsObj, `${errorCategory}.${featureId}`, null);
        if (featureObj.pass) continue;
        // if (_.get(featureObj, 'feedback.correct') === false) continue; // already provided good feedback
        if (_.find(target, t => t.feature_id === featureId && t.detail === featureObj.detail)) continue; // already in the list
        if (!_.isNull(featureObj)) {
          target.push({
            feature_id: Number(featureId),
            line_item_name: featureObj.detail,
            is_inference_correct: false,
          });
        }
      }
    }

    if (_.isEmpty(target)) return;

    for (const featureObj of target) {
      const res = await annotate({
        product_id: Number(ipcProductId),
        step: 0,
        feature_id: featureObj.feature_id,
        line_item_name: featureObj.line_item_name,
        is_inference_correct: false,
      });

      if (res.error) {
        aoiAlert(t('notification.error.provideFeedback'), ALERT_TYPES.COMMON_ERROR);
        console.error('annotate error:', _.get(res, 'error.message', ''));
        dispatch(setIsContainerLvlLoadingEnabled(false));
        return;
      }
    }

    dispatch(setIsContainerLvlLoadingEnabled(false));
    refetchInspectedFeatures();
  };

  const handleProvideGoodFeedbackForAll = async (ipcProductId, goldenProduct) => {
    // const res = await submitGoodFeedbackForAll({
    //   product_id: Number(ipcProductId),
    //   variant: _.get(goldenProduct, 'product_name'),
    // });

    // if (res.error) {
    //   aoiAlert(t('notification.error.provideGoodFeedbackForAllPredictedAsDefectInspectionItem'), ALERT_TYPES.COMMON_ERROR);
    //   console.error('submitGoodFeedbackForAll error:', _.get(res, 'error.message', ''));
    //   return;
    // }

    // TODO: loop through all line items and provide good feedback
  };

  const handleSubmitConveyorOperation = async (accessToken, operation) => {
    if (_.isEmpty(accessToken)) {
      aoiAlert(t('notification.error.conveyorAccessTokenEmpty'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await submitConveyorOperation({
      conveyor_access_token: accessToken,
      op: operation,
    });

    if (res.error) {
      aoiAlert(t('notification.error.conveyorOperation'), ALERT_TYPES.COMMON_ERROR);
      console.error('submitConveyorOperation error:', _.get(res, 'error.message', ''));
      return;
    }
  };

  const handleBackToLive = async (ipcSessionId, slotId, goldenProductId) => {
    let status;
    try {
      status = await getCurrentConveyorStatus(
        lazyGetConveyorStatus,
        lazyGetInferenceStatus,
        t,
      );
    } catch (e) {
      console.error('failed to get conveyor status', e);
      aoiAlert(t('notification.error.getAllConveyorStatus'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (_.get(status, `${slotId}.info.session_id`) !== Number(ipcSessionId)) {
      aoiAlert(t('notification.error.inspectionSessionId'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    navigate(`/inspection/live?running-session-id=${ipcSessionId}&slot-id=${slotId}&golden-product-id=${goldenProductId}`);
  };

  // Function to filter components based on wasted sub boards
  const getFilteredComponents = (components, subunits) => {
    if (_.isEmpty(subunits)) return components;

    const wastedArrayIndices = new Set(
      _.filter(subunits, su => su.wasted).map(su => su.array_index)
    );

    return _.filter(components, component => {
      const arrayIndex = _.get(component, 'array_index');
      return !wastedArrayIndices.has(arrayIndex);
    });
  };

  useEffect(() => {
    if (_.isUndefined(inspectedComponents) || _.isUndefined(inspectedFeatures)) return;
    if (_.isEmpty(inspectedComponents) && _.isEmpty(inspectedFeatures)) {
      setParsedComponents(null);
      return;
    }

    // let feedbackProvided = 0;

    // we want component at the first lvl, all error details under it as second lvl
    // and all the features under it as third lvl
    const newComponentsObj = {};
    const componentsObj = {};
    const rcidToResultCompomnentMap = {};

    // Filter components based on wasted sub boards when on review tab
    const componentsToProcess = activeTab === 'review'
      ? getFilteredComponents(inspectedComponents, subUnitInfo)
      : inspectedComponents;

    for (const rc of componentsToProcess) {
      rcidToResultCompomnentMap[`${rc.result_component_id}`] = rc;
      newComponentsObj[`${rc.result_component_id}`] = {
        ...rc,
        features: {},
      };
    }

    for (const agentResult of inspectedFeatures) {
      if (!_.has(newComponentsObj, `${agentResult.component_id}`)) continue;
      let curParsedError;
      try {
        curParsedError = JSON.parse(_.get(agentResult, 'error', '{}'));
      } catch (e) {
        console.error('failed to parse error', e);
        continue;
      }
      newComponentsObj[`${agentResult.component_id}`].features[`${agentResult.feature_id}`] = {
        featureType: agentResult.feature_type,
        featureId: agentResult.feature_id,
        component_id: agentResult.component_id,
        agentResult: {
          ..._.get(newComponentsObj, `[${agentResult.component_id}].features[${agentResult.feature_id}].agentResult`, {}),
          [`${agentResult.error_type}`]: {
            pass: agentResult.pass,
            error: curParsedError,
            feedback: agentResult.feedback,
            reevaluationResult: agentResult.reevaluation_result,
            detail: agentResult.detail,
            errorType: agentResult.error_type,
          }
        },
        inspectionResultId: agentResult.inspection_result_id,
        traniningExample: agentResult.training_example,
        roi: agentResult.roi,
      };
    }

    setParsedComponents(newComponentsObj);

    // console.log('newComponentsObj', newComponentsObj);

    if (_.isEmpty(newComponentsObj)) {
      setSelectedRCid(null);
      setSelectedDCid(null);
      setSelectedFid(null);
      setSelectedErrorCategory(null);
      setSelectedInspectionResultId(null);
      return;
    }

    if (pageInitialized.current) return;

    const firstCId = Number(_.keys(newComponentsObj)[0]);
    // console.log('firstCId', firstCId);
    setSelectedRCid(firstCId);
    setSelectedDCid(_.get(newComponentsObj, `[${firstCId}].definition_component_id`, null));
    const firstFeatureId = Number(_.get(_.keys(_.get(newComponentsObj, `[${firstCId}].features`)), '[0]', null));
    // console.log('firstFeatureId', firstFeatureId);
    if (!_.isInteger(firstFeatureId)) return;
    setSelectedFid(firstFeatureId);
    const firstComponentErrorCategory = _.get(_.keys(_.get(newComponentsObj, `[${firstCId}].features[${firstFeatureId}].agentResult`)), '[0]', null);
    // console.log('firstComponentErrorCategory', firstComponentErrorCategory);
    setSelectedErrorCategory(firstComponentErrorCategory);
    setSelectedInspectionResultId(_.get(newComponentsObj, `[${firstCId}].features[${firstFeatureId}].inspectionResultId`, null));
    setSelectedArrayIndex(_.get(newComponentsObj, `[${firstCId}].array_index`, null));

    pageInitialized.current = true;
  }, [inspectedComponents, inspectedFeatures, activeTab, subUnitInfo]);

  useEffect(() => {
    refetchSessionInfo();
    allInspectionCountReq.refetch();
  }, []);

  useEffect(() => {
    refetchNeighborInspections();
  }, [
    ipcProductId,
    ipcSessionId,
  ]);

  // Fetch subunit information from the latest inspection
  useEffect(() => {
    const fetchSubUnitInfo = async () => {
      if (!ipcSessionId) return;

      try {
        const latestIpcRes = await fetch(`${serverHost}/allInspections?ipc_session_id=${ipcSessionId}&limit=1`, {
          headers: {
            Authorization: localStorage.getItem('accessToken'),
          },
        });

        if (latestIpcRes.ok) {
          const data = await latestIpcRes.json();
          const subunits = _.get(data, '[0].subunits', []);
          setSubUnitInfo(subunits);
        }
      } catch (error) {
        console.error('Failed to fetch subunit info:', error);
      }
    };

    fetchSubUnitInfo();
  }, [ipcSessionId]);

  return (
    <div className='flex justify-center items-center flex-1 self-stretch'>
      {/* <div
        className={`flex py-4 px-0.5 flex-col self-stretch border-r-[2px] border-r-[#0000001a] bg-[#ffffff08]`}
        style={{
          width: `${conveyorControllerWidth}px`,
        }}
      >
        <ConfigProvider
          theme={{
            components: {
              Menu: {
                itemHeight: 73,
              }
            }
          }}
        >
          <CustomMenu
            paddingblock={11}
            selectable={false}
            onChange={({ key }) => {
              let operation = '';
              switch (key) {
                case 'PCBIn':
                  operation = conveyorOperation.load;
                  break;
                case 'PCBOut':
                  operation = conveyorOperation.unload;
                  break;
                case 'passThru':
                  operation = conveyorOperation.skip;
                  break;
                default:
                  return;
              }
              handleSubmitConveyorOperation(conveyorAccessToken, operation);
            }}
            items={[
              {
                key: 'continuous',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch '>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img src='/icn/continuous_white.svg' alt='continuous' className='w-6 h-6 opacity-50' />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.continuous')}
                  </span>
                </div>,
                disabled: true,
              },
              {
                key: 'inspect',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch '>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img src='/icn/inspect_white.svg' alt='inspect' className='w-6 h-6 opacity-50' />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.inspect')}
                  </span>
                </div>,
                disabled: true,
              },
              {
                key: 'stop',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/stop_white.svg'
                      alt='stop'
                      className='w-6 h-6 opacity-50'
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.stop')}
                  </span>
                </div>,
                disabled: true,
              },
              {
                key: 'PCBIn',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/pcbIn_color.svg'
                      alt='stop'
                      className='w-6 h-6'
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.PCBIn')}
                  </span>
                </div>,
                // disabled: _.isEmpty(conveyorAccessToken) && recipeActiveTab === 'conveyorSetup',
              },
              {
                key: 'PCBOut',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className={`flex w-8 h-8 justify-center items-center`}>
                    <img
                      src='/icn/pcbOut_color.svg'
                      alt='stop'
                      className={`w-6 h-6`}
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.PCBEject')}
                  </span>
                </div>,
                // disabled: _.isEmpty(conveyorAccessToken) || _.includes(['PCBDimension', 'fullPCBCapture'], recipeActiveTab),
              },
              // {
              //   key: 'clampOn',
              //   label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
              //     <div className='flex w-8 h-8 justify-center items-center'>
              //       <img
              //         src='/icn/clampOn_color.svg'
              //         alt='stop'
              //         className='w-6 h-6'
              //       />
              //     </div>
              //     <span className='font-source text-[10px] font-normal leading-[150%]'>
              //       {t('productDefine.clampOn')}
              //     </span>
              //   </div>
              // },
              // {
              //   key: 'clampOff',
              //   label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
              //     <div className='flex w-8 h-8 justify-center items-center'>
              //       <img
              //         src='/icn/clampOff_color.svg'
              //         alt='stop'
              //         className='w-6 h-6'
              //       />
              //     </div>
              //     <span className='font-source text-[10px] font-normal leading-[150%]'>
              //       {t('productDefine.clampOff')}
              //     </span>
              //   </div>
              // },
              {
                key: 'passThru',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/fastForward_white.svg'
                      alt='stop'
                      className='w-6 h-6'
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.passThru')}
                  </span>
                </div>,
                // disabled: _.isEmpty(conveyorAccessToken),
              },
              {
                key: 'reset',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/reset_white.svg'
                      alt='stop'
                      className='w-6 h-6 opacity-50'
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.reset')}
                  </span>
                </div>,
                // disabled: _.isEmpty(conveyorAccessToken),
                // disabled: true,
              },
            ]}
          />
        </ConfigProvider>
      </div> */}
      <div className='flex flex-1 flex-col self-stretch'>
        <div className='flex px-6 py-3 justify-center items-center gap-2 self-stretch bg-[#56ccf21a] border-b-[1px] border-b-[#333]'>
          <div className='flex flex-1 gap-6 self-stretch items-center'>
            <div className='flex items-center gap-2'>
              <span className='font-source text-[14px] font-normal leading-[150%] text-gray-4'>
                {t('productDefine.taskID')}:
              </span>
              <span className='font-source text-[14px] font-semibold leading-[150%]'>
                {ipcSessionId}
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='font-source text-[14px] font-normal leading-[150%] text-gray-4'>
                {t('productDefine.product')}:
              </span>
              <span className='font-source text-[14px] font-semibold leading-[150%]'>
                {_.get(goldenProduct, 'product_name', '')}
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='font-source text-[14px] font-normal leading-[150%] text-gray-4'>
                {t('productDefine.sn')}:
              </span>
              <span className='font-source text-[14px] font-semibold leading-[150%]'>
                {_.get(inspectedProduct, 'serial_number', '')}
              </span>
              <Button
                type='text'
                onClick={() => {
                  const run = async (ipcProducId) => {
                    const res = await scanBarcode();

                    if (res.error) {
                      aoiAlert(t('notification.error.scanBarcode'), ALERT_TYPES.COMMON_ERROR);
                      return;
                    }

                    if (res) {
                      if(!res.data) {
                        aoiAlert(t('notification.error.scanBarcode'), ALERT_TYPES.COMMON_ERROR);
                        return;
                      }

                      const updateRes = await updateProduct({
                        product_id: Number(ipcProducId),
                        product_serial: res?.data
                      });

                      if (updateRes.error) {
                        aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
                        console.error('updateProduct error: ', updateRes.error.message);
                        return;
                      }

                      const updateResData = _.get(updateRes, 'data.data', {});
                      if (updateResData) {
                        aoiAlert(t('notification.success.updateProduct'), ALERT_TYPES.COMMON_SUCCESS);
                        await refetchIpcProduct();
                      } else {
                        aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
                      }
                    }
                  };

                  run(ipcProductId);
                }}
              >
                <span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {t('worklist.rescan')}
                </span>
              </Button>
            </div>
            <div className='flex px-2 items-center gap-1'>
              <div
                className={`flex w-[24px] h-[24px] flex-col justify-center items-center rounded-[2px] hover:bg-[#ffffff08]
                  transition-all duration-300 ease-in-out ${!_.isNumber(_.get(neighborInspections, 'newer_product_id')) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                onClick={() => {
                  if (_.isEmpty(neighborInspections)) return;
                  if (isErrorNeighborInspections || isLoadingNeighborInspections || isFetchingNeighborInspections) return;
                  if (!_.isNumber(_.get(neighborInspections, 'newer_product_id'))) return;
                  pageInitialized.current = false; // reset pageInitialized to avoid first render issue
                  let url = `/inspection/review?ipc-product-id=${_.get(neighborInspections, 'newer_product_id')}&golden-product-id=${goldenProductId}&ipc-session-id=${ipcSessionId}`;
                  if (!_.isEmpty(worklistQuery)) {
                    url = url.concat(`&query=${encodeURIComponent(JSON.stringify(worklistQuery))}`);
                  }
                  if (isFromLive) url = url.concat(`&is-from-live=${isFromLive}&slot-id=${slotId}`);
                  navigate(url);
                }}
              >
                <img
                  src='/icn/arrowLeft_white.svg'
                  className='w-[5px] h-[10px]'
                  alt='arrowLeft'
                />
              </div>
              <InputNumber
                controls={false}
                min={1}
                value={_.get(neighborInspections, 'location', 0)}
                style={{ width: '80px' }}
                // onChange={(value) => {}}
              />
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                /
              </span>
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {_.get(allInspectionCountReq, 'data.pageCount', 0)}
              </span>
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('productDefine.items')}
              </span>
              <div
                className={`flex w-[24px] h-[24px] flex-col justify-center items-center rounded-[2px] hover:bg-[#ffffff08]
                  ${!_.isNumber(_.get(neighborInspections, 'older_product_id')) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} transition-all duration-300 ease-in-out`}
                onClick={() => {
                  if (_.isEmpty(neighborInspections)) return;
                  if (isErrorNeighborInspections || isLoadingNeighborInspections || isFetchingNeighborInspections) return;
                  if (!_.isNumber(_.get(neighborInspections, 'older_product_id'))) return;
                  pageInitialized.current = false; // reset pageInitialized to avoid first render issue
                  let url = `/inspection/review?ipc-product-id=${_.get(neighborInspections, 'older_product_id')}&golden-product-id=${goldenProductId}&ipc-session-id=${ipcSessionId}`;
                  if (!_.isEmpty(worklistQuery)) {
                    url = url.concat(`&query=${encodeURIComponent(JSON.stringify(worklistQuery))}`);
                  }
                  if (isFromLive) url = url.concat(`&is-from-live=${isFromLive}&slot-id=${slotId}`);
                  navigate(url);
                }}
              >
                <img
                  src='/icn/arrowLeft_white.svg'
                  alt='arrowLeft'
                  className='w-[5px] h-[10px] rotate-180'
                />
              </div>
            </div>
            {isFromLive && (
              <Button
                onClick={() => {
                  if (_.isUndefined(slotId)) return;
                  handleBackToLive(ipcSessionId, slotId, goldenProductId);
                }}
              >
                <span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {t('review.backToLive')}
                </span>
              </Button>
            )}
            {!isFromLive && (
              <Button
                onClick={() => {
                  navigate(`/worklist?prev-ipc-session-id=${ipcSessionId}`);
                }}
              >
                <span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {t('review.backToWroklist')}
                </span>
              </Button>
            )}
          </div>
          <div className='flex gap-2'>
            <Button
              size='large'
              onClick={() => {
                const run = async (pid) => {
                  const res = await fetch(`${serverHost}/inspection/exportMesResult`, {
                    method: 'POST',
                    headers: {
                      Authorization: localStorage.getItem(localStorageKeys.accessToken),
                    },
                    body: JSON.stringify(Number(pid)),
                  });

                  if (!res.ok) {
                    aoiAlert(t('notification.error.exportMesResult'), ALERT_TYPES.COMMON_ERROR);
                    console.error('exportMesResult error: ', res.statusText);
                    return;
                  }

                  aoiAlert(t('notification.success.exportMesResult'), ALERT_TYPES.COMMON_INFO);
                  return;
                };

                run(ipcProductId);
              }}
            >
              <span className='font-source text-[12px] font-semibold leading-[150%]'>{t('common.exportMESResult')}</span>
            </Button>
            <Button
              size='large'
            >
              <div className='flex gap-2 items-center'>
                <img src='/icn/thumbdown_red.svg' alt='thumbdown' className='w-[12px] h-[11px]' />
                <span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.failBoard')}</span>
              </div>
            </Button>
          </div>
        </div>
        <div className='flex flex-1 px-0.5 gap-0.5 self-stretch rounded-[6px]'>
          {/* <div className='flex w-[617px] items-center self-stretch'> */}
          <div className='flex items-center self-stretch flex-shrink-0'>
            {/* defect list col */}
            <div className='flex w-[236px] flex-col shrink-0 self-stretch border-r-[2px] border-r-[#000]'>
              {/* Tab header */}
              {!_.isEmpty(subUnitInfo) && (
                <div className='flex items-center self-stretch border-b-[1px] border-b-[#4F4F4F]'>
                  <ConfigProvider
                    theme={{
                      components: {
                        Tabs: {
                          cardPadding: '6px 12px',
                          horizontalMargin: '4px 4px 0 4px',
                          colorBgContainer: '#ffffff0d',
                          colorBorder: '#4F4F4F',
                          cardBg: '#1E1E1E',
                          itemSelectedColor: '#fff',
                          itemHoverColor: '#fff',
                          itemActiveColor: '#fff',
                        },
                      }
                    }}
                  >
                    <Tabs
                      activeKey={activeTab}
                      onChange={(key) => setActiveTab(key)}
                      type='card'
                      size='small'
                      items={[
                        {
                          key: 'review',
                          label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {t('worklist.review')}
                          </span>,
                        },
                        {
                          key: 'arrayInfo',
                          label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {t('review.arrayInfo')}
                          </span>,
                        },
                      ]}
                    />
                  </ConfigProvider>
                </div>
              )}

              {/* Content header */}
              <div className='flex py-4 px-2 flex-col gap-4 self-stretch'>
                <span className='font-source text-[16px] font-normal leading-[150%]'>
                  {activeTab === 'review' ? t('productDefine.defectTotal') : t('review.wastedSubBoardInfo')}:
                </span>
                <div className='flex gap-2 items-center'>
                  {/* Buttons removed for cleaner interface */}
                </div>
              </div>
              <div className='flex px-1 flex-col flex-1 self-stretch'>
                {/* Tab content */}
                {activeTab === 'review' && (
                  <div
                    className='flex flex-col gap-0.5 self-stretch'
                    style={{
                      height: `calc(100vh - ${!_.isEmpty(subUnitInfo) ? '313px' : '273px'} + 40px)`,
                      overflowY: 'auto',
                    }}
                  >
                  {_.map(_.keys(parsedComponents), (rcid, idx) => {
                    let errorCount = 0;
                    let firstError = '';

                    for (const featureId of _.keys(_.get(parsedComponents, `${rcid}.features`, {}))) {
                      const featureObj = _.get(parsedComponents, `${rcid}.features.${featureId}`, null);
                      if (!_.isEmpty(_.get(featureObj, 'agentResult', {}))) {
                        errorCount += _.keys(_.get(featureObj, 'agentResult', {})).length;
                        if (_.isEmpty(firstError)) firstError = _.first(_.keys(_.get(featureObj, 'agentResult', {})));
                      }
                    }

                    return (
                    <div
                      key={idx}
                      className={`flex py-1 px-2 flex-col self-stretch border-l-[3px] transition-all duration-300 ease-in-out cursor-pointer
                        ${selectedRCid === Number(rcid) ? 'border-l-[#56CCF2] bg-[#56ccf21a]' : 'border-l-[#828282]'}`}
                      onClick={() => {
                        setMaskInfo({ masks: [], selectedIndex: 0, show: false });

                        if (selectedRCid === Number(rcid)) {
                          // setSelectedRCid(null);
                          // setSelectedFid(null);
                          // setSelectedDCid(null);
                          // setSelectedErrorCategory(null);
                          // setSelectedInspectionResultId(null);
                          return;
                        }

                        setSelectedRCid(Number(rcid));
                        setSelectedDCid(Number(_.get(parsedComponents, `${rcid}.definition_component_id`, null)));
                        const firstFeatureId = Number(_.keys(_.get(parsedComponents, `${rcid}.features`, {}))[0]);
                        if (!_.isInteger(firstFeatureId)) return;
                        setSelectedFid(firstFeatureId);
                        const firstErrorType = _.keys(_.get(parsedComponents, `${rcid}.features.${firstFeatureId}.agentResult`, {}))[0];
                        if (!_.isString(firstErrorType)) return;
                        setSelectedErrorCategory(firstErrorType);
                        setSelectedInspectionResultId(_.get(parsedComponents, `[${rcid}].features[${firstFeatureId}].inspectionResultId`, null));
                        setSelectedArrayIndex(_.get(parsedComponents, `[${rcid}].array_index`, null));
                      }}
                    >
                      <div className='flex py-1 px-2 items-center self-stretch'>
                        <span className='font-source text-[12px] font-semibold leading-[150%]'>
                          {/* {f.designator}-{f.result_component_id}/{f.feature_type} */}
                          {_.get(parsedComponents, `${rcid}.designator`, '')}
                        </span>
                        </div>
                      <div className='flex py-1 px-2 items-center justify-between gap-2 self-stretch'>
                        <div className='flex items-center self-stretch gap-2'>
                          {errorCount > 0 &&
                            <Fragment>
                              <div className='flex items-center gap-1'>
                                <div className='flex w-4 h-4 justify-center items-center'>
                                  <img src='/icn/warnCircleFilled_red.svg' alt='warnCircleFilled' className='w-[12px] h-[10.9px]' />
                                </div>
                                <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                                  {firstError !== ''  ? t(`inferenceErrorType.${firstError}`) : t(`inferenceErrorType.errorNotFound`)}
                                </span>
                              </div>
                              {errorCount > 1 && (
                                <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                                  {`+${errorCount - 1} ${t('common.more')}`}
                                </span>
                              )}
                            </Fragment>
                          }
                        </div>
                        {!_.isEmpty(_.get(parsedComponents, `${rcid}.feedback_error_type`, '')) && _.get(parsedComponents, `${rcid}.feedback_error_type`) === 'no_error' && (
                          <img
                            src='/icn/thumbup_green.svg'
                            alt='thumbup'
                            className='w-[12px] h-[11px] opacity-50'
                          />
                        )}
                        {!_.isEmpty(_.get(parsedComponents, `${rcid}.feedback_error_type`, '')) && _.get(parsedComponents, `${rcid}.feedback_error_type`) !== 'no_error' && (
                          <img
                            src='/icn/thumbdown_red.svg'
                            alt='thumbdown'
                            className='w-[12px] h-[11px] opacity-50'
                          />
                        )}
                      </div>
                      {!_.isEmpty(subUnitInfo) && (
                        <div className='flex items-center py-1 px-2 gap-2 self-stretch'>
                          <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {t('review.arrayIndex')}: {_.get(parsedComponents, `${rcid}.array_index`, '')}
                          </span>
                          {/* {!_.isEmpty(subUnitInfo) && _.get(_.find(subUnitInfo, su => su.array_index === _.get(parsedComponents, `${rcid}.array_index`, '')), 'serial_number', null) ? (
                            <span className='font-source text-[12px] font-normal leading-[150%]'>
                              {t('review.subBoardSN')}: {_.find(subUnitInfo, su => su.array_index === _.get(parsedComponents, `${rcid}.array_index`, '')).serial_number}
                            </span>
                          ) : (
                            <span className='font-source text-[12px] font-normal leading-[150%]'>
                              {t('review.subBoardSNNotFound')}
                            </span>
                          )} */}
                        </div>
                      )}
                    </div>
                    );
                  })}
                  </div>
                )}

                {/* Array Info Tab Content */}
                {activeTab === 'arrayInfo' && !_.isEmpty(subUnitInfo) && (
                  <div className='flex flex-col items-start flex-1 self-stretch p-2'>
                    <div className='flex flex-col items-start gap-px self-stretch p-1 flex-1'>
                      <CommonTable
                        cols={[
                          {
                            key: 'array_index',
                            title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('liveInspection.arrayIndex')}</span>,
                            render: (text, record) => (
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {record.array_index}
                              </span>
                            ),
                          },
                          {
                            key: 'failure_ratio',
                            title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('liveInspection.failureRatio')}</span>,
                            render: (text, record) => (
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {(record.failure_ratio * 100).toFixed(1)}%
                              </span>
                            ),
                          }
                        ]}
                        data={_.filter(subUnitInfo, su => su.wasted)}
                        total={_.filter(subUnitInfo, su => su.wasted).length}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
            {/* feedback detail col */}
            <div className='flex py-4 px-3 flex-col gap-4 w-[576px] self-stretch border-r-[2px] border-r-[#000]'>
              <div className='flex flex-col gap-2 self-stretch'>
                <span className='font-source text-[16px] font-semibold leading-[150%]'>
                  {_.get(parsedComponents, `${selectedRCid}.designator`, '')}
                </span>
                <div className='flex-col flex gap-1 self-stretch'>
                  <div className='flex items-center gap-2 self-stretch'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.partNo')}:
                    </span>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {_.get(parsedComponents, `${selectedRCid}.part_no`, '')}
                    </span>
                  </div>
                  <div className='flex items-center gap-2 self-stretch'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.packageNo')}:
                    </span>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {_.get(parsedComponents, `${selectedRCid}.package_no`, '')}
                    </span>
                  </div>
                </div>
                {/* <Button
                  style={{ width: '209px' }}
                  onClick={() => {
                    handleComponentPassClick(parsedComponents, selectedRCid, ipcProductId);
                  }}
                >
                  <div className='flex items-center gap-2 self-stretch'>
                    <img src='/icn/thumbup_green.svg' alt='thumbup' className='w-[12px] h-[11px]' />
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.pass')}
                    </span>
                  </div>
                </Button> */}
              </div>
              <div className='flex gap-0.5 flex-1 self-stretch'>
                <div
                  className='flex w-[209px] flex-col gap-1 self-stretch overflow-y-auto'
                  style={{
                    height: 'calc(100vh - 269px)',
                  }}
                >
                  <div className='flex px-1 justify-center items-center gap-2.5 self-stretch'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('review.defectFeedback')}
                    </span>
                  </div>
                  {/* {!_.isEmpty(_.get(parsedComponents, `${selectedRCid}.errors`, {})) && _.map(_.keys(_.get(parsedComponents, `${selectedRCid}.errors`, {})), (err, idx) => (
                    <div
                      className={`flex py-3 justify-center items-center gap-0.5 self-stretch rounded-[6px]
                        ${selectedErrorCategory !== err ? 'bg-[#ffffff08]' : 'bg-[#EB5757]'}
                        cursor-pointer transition-all duration-300 ease-in-out`}
                      key={idx}
                      onClick={() => {
                        if (selectedErrorCategory === err) {
                          setSelectedErrorCategory(null);
                          setSelectedFid(null);
                          setSelectedDetail(null);
                          setSelectedInspectionResultId(null);
                          return;
                        }
                        const firstFeatureId = Number(_.keys(_.get(parsedComponents, `${selectedRCid}.errors.${err}`, {}))[0]);
                        const firstFeatureDetail = _.get(parsedComponents, `${selectedRCid}.errors.${err}.${firstFeatureId}.detail`, null);
                        setSelectedFid(firstFeatureId);
                        setSelectedDetail(firstFeatureDetail);
                        setSelectedErrorCategory(err);
                        setSelectedInspectionResultId(_.get(parsedComponents, `${selectedRCid}.errors.${err}.${firstFeatureId}.inspectionResultId`, null));
                      }}
                    >
                      <span className={`font-source text-[14px]
                      ${selectedErrorCategory.category === err ? 'font-semibold' : 'font-normal'} leading-[150%]`}>
                        {err}
                      </span>
                    </div>
                  ))} */}

                  {/* feed back error types */}
                  {_.map(_.keys(_.get(systemMetadata, 'error_type_to_code', {})), (err, idx) => (
                    <div key={idx} className='flex items-center gap-2 self-stretch'>
                      <Tooltip
                        title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                          {t(`inferenceErrorType.${err}`)}
                        </span>}
                        placement='left'
                      >
                        <div
                          className={`flex py-3 justify-center items-center gap-0.5 self-stretch rounded-[6px]
                            ${_.get(parsedComponents, `[${selectedRCid}].predicted_error_type`) !== err ? 'bg-[#ffffff08]' : 'bg-[#EB5757]'}
                            cursor-pointer transition-all duration-300 ease-in-out flex-1`}
                          onClick={() => {
                            const run = async (selectedRCid, err, ipcProductId, arrayIndex) => {
                              const res = await annotateGroup({
                                product_id: Number(ipcProductId),
                                group_id: Number(selectedRCid),
                                step: 0,
                                error_type: err,
                                array_index: arrayIndex,
                              });

                              if (res.error) {
                                aoiAlert(t('notification.error.annotateGroup'), ALERT_TYPES.COMMON_ERROR);
                                console.error('annotateGroup error:', _.get(res, 'error.message', ''));
                                return;
                              }

                              await refetchInspectedComponents();
                              await refetchInspectedFeatures();
                            };

                            // run(selectedRCid, err, ipcProductId, _.get(parsedComponents, `[${selectedRCid}].array_index`, null));
                            run(selectedRCid, err, ipcProductId, selectedArrayIndex);
                          }}
                        >
                          <span className={`font-source text-[14px]
                          ${_.get(parsedComponents, `[${selectedRCid}].predicted_error_type`) === err ? 'font-semibold' : 'font-normal'} leading-[150%]`}>
                            {t(`inferenceErrorType.${err}`)} {getShortcutText(idx)}
                          </span>
                        </div>
                      </Tooltip>
                      {_.get(parsedComponents, `[${selectedRCid}].feedback_error_type`) === err && (
                        <Tooltip
                          title={<span className='font-source text-[12px] font-normal leading-[150%]'>{t('review.thisErrorTypeWasFeedbacked')}</span>}
                        >
                          <div className='flex w-6 h-6 justify-center items-center'>
                            {_.get(parsedComponents, `[${selectedRCid}].feedback_error_type`) !== 'no_error' ? (
                              <img
                                src='/icn/thumbdown_red.svg'
                                alt='thumbdown'
                                className='w-[12px] h-[12px]'
                              />
                            ) : (
                              <img
                                src='/icn/thumbup_green.svg'
                                alt='thumbup'
                                className='w-[12px] h-[12px]'
                              />
                            )}
                            {/* <img
                              src='/icn/twoWay_gray.svg'
                              className='w-[12px] h-[12px]'
                              alt='twoWay_gray'
                            /> */}
                          </div>
                        </Tooltip>
                      )}
                    </div>
                  ))}
                </div>
                <div
                  className='flex self-stretch flex-col px-4 bg-[#ffffff08] gap-2 overflow-y-auto flex-1'
                  style={{
                    height: 'calc(100vh - 269px)',
                  }}
                >
                  {_.isInteger(selectedInspectionResultId) && import.meta.env.DEV &&
                    <Button
                      onClick={() => {
                        if (!_.isInteger(selectedInspectionResultId)) return;

                        const run = async (selectedInspectionResultId) => {
                          const res = await exportInspectedFeature({
                            inspection_result_id: selectedInspectionResultId,
                          });

                          if (res.error) {
                            aoiAlert(t('notification.error.exportInspectedFeature'), ALERT_TYPES.COMMON_ERROR);
                            console.error('exportInspectedFeature error:', _.get(res, 'error.message', ''));
                            return;
                          }

                          window.open(`${serverHost}/file?file_uri=${_.get(res, 'data.data_uri')}`, '_blank');
                        };

                        run(selectedInspectionResultId);
                      }}
                    >
                      <span className='font-source text-[12px] font-semibold leading-[150%]'>
                        {t('review.exportThisAgentResult')}
                      </span>
                    </Button>
                  }
                  <Button
                    onClick={() => {
                      handleCreateComponentVariantion(
                        selectedRCid,
                        ipcProductId,
                        0,
                        _.get(parsedComponents, `[${selectedRCid}].predicted_error_type`, null),
                        selectedArrayIndex,
                      )
                    }}
                  >
                    <span className='font-source text-[12px] font-semibold leading-[150%]'>
                      {t('review.setAsAlternative')}
                    </span>
                  </Button>
                  <div className='flex flex-col gap-2 self-stretch flex-1'>
                    {/* {_.isInteger(selectedRCid) && !_.isEmpty(selectedErrorCategory) && !_.isEmpty(selectedErrorCategory) &&
                      <Collapse
                        activeKey={_.isInteger(selectedFid) ? [String(selectedFid)] : null}
                        onChange={(keys) => {
                          if (_.isEmpty(keys)) {
                            setSelectedFid(null);
                            setSelectedDetail(null);
                            setSelectedInspectionResultId(null);
                            return;
                          }
                          if (keys.length === 2) {
                            setSelectedFid(Number(_.get(keys, '[1]', null)));
                            setSelectedDetail(_.get(parsedComponents, `${selectedRCid}.errors.${selectedErrorCategory}.${_.get(keys, '[1]', null)}.detail`, null));
                            setSelectedInspectionResultId(_.get(parsedComponents, `${selectedRCid}.errors.${selectedErrorCategory}.${_.get(keys, '[1]', null)}.inspectionResultId`, null));
                            return;
                          }
                          if (keys.length === 1) {
                            setSelectedFid(Number(_.get(keys, '[0]', null)));
                            setSelectedDetail(_.get(parsedComponents, `${selectedRCid}.errors.${selectedErrorCategory}.${_.get(keys, '[0]', null)}.detail`, null));
                            setSelectedInspectionResultId(_.get(parsedComponents, `${selectedRCid}.errors.${selectedErrorCategory}.${_.get(keys, '[0]', null)}.inspectionResultId`, null));
                            return;
                          }
                        }}
                        items={_.map(_.keys(_.get(parsedComponents, `${selectedRCid}.errors.${selectedErrorCategory}`, {})), (fid, idx) => (
                          {
                            key: String(fid),
                            label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                              {_.get(parsedComponents, `${selectedRCid}.errors.${selectedErrorCategory}.${fid}.featureType`, '')}
                            </span>,
                            children: <div className='flex items-center px-2 self-stretch'>
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {_.get(parsedComponents, `${selectedRCid}.errors.${selectedErrorCategory}.${fid}.errorInfo`, '')}
                              </span>
                            </div>,
                          }
                        ))}
                      />
                    } */}
                    <AllFeaturesErrorInfo
                      parsedComponents={parsedComponents}
                      selectedRCid={selectedRCid}
                      systemMetadata={systemMetadata}
                      goldenProductId={goldenProductId}
                      selectedFid={selectedFid}
                      setSelectedFid={setSelectedFid}
                      setSelectedInspectionResultId={setSelectedInspectionResultId}
                      goldenFeatures={goldenFeatures}
                      onMaskChange={setMaskInfo}
                      maskInfo={maskInfo}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className='flex p-1 flex-col gap-0.5 flex-1 justify-center self-stretch'>
            <InspectionReviewDisplay
              goldenFeatures={goldenFeatures}
              goldenComponents={goldenComponents}
              inspectedFeatures={inspectedFeatures}
              inspectedComponents={inspectedComponents}
              selectedRCid={selectedRCid}
              selectedDCid={selectedDCid}
              selectedFid={selectedFid}
              goldenProduct={goldenProduct}
              selectedArrayIndex={selectedArrayIndex}
              maskImage={maskInfo.show ? maskInfo.masks : null}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const AllFeaturesErrorInfo = (props) => {
  const {
    parsedComponents,
    selectedRCid,
    systemMetadata,
    goldenProductId,
    selectedFid,
    setSelectedFid,
    setSelectedInspectionResultId,
    goldenFeatures,
    onMaskChange,
    maskInfo,
  } = props;

  const { t } = useTranslation();

  const [parsedFeatures, setParsedFeatures] = useState([]);

  // const [lazyGetFeatureByFeatureId] = useLazyGetFeatureByFeatureIdQuery();

  useEffect(() => {
    if (!goldenFeatures) return;

    onMaskChange({ masks: [], selectedIndex: 0, show: false });

    // re-generate parsed features since we need line item params for reference value
    const run = async (selectedRCid, parsedComponents, systemMetadata, goldenProductId, goldenFeatures) => {
      const newParsedFeatures = [];
      const rawFeatures = _.get(parsedComponents, `[${selectedRCid}].features`, {});

      for (const featureId of _.keys(rawFeatures)) {
        if (_.isEmpty(_.get(rawFeatures, `${featureId}.agentResult`, {}))) continue;

        const parsedError = {};
        let mask = null;

        for (const mainErrorType of _.keys(_.get(rawFeatures, `${featureId}.agentResult`, {}))) {
          // record mask if exists
          if (!mask) {
            const errorDetail = _.get(rawFeatures, `${featureId}.agentResult.${mainErrorType}.detail`, null);
            const errDetailObj = _.get(rawFeatures, `${featureId}.agentResult.${mainErrorType}.error.error_detail`, {});
            if (errorDetail === leadInspection2D) {
              mask = {
                solder: _.map(_.get(errDetailObj, 'solder_components', []), sc => ({ image: sc.mask_image, roi: sc.roi })),
                pad: _.map(_.get(errDetailObj, 'pad_components', []), pc => ({ image: pc.mask_image, roi: pc.roi })),
                tip: _.map(_.get(errDetailObj, 'tip_components', []), tc => ({ image: tc.mask_image, roi: tc.roi })),
              };
            } else if (errorDetail === solderInspection2D) {
              mask = _.get(errDetailObj, 'result_image_jpeg', null);
            } else {
              mask = _.get(errDetailObj, 'result_image_jpeg', null);
            }
          }
          for (const errorType of _.keys(_.get(rawFeatures, `${featureId}.agentResult.${mainErrorType}.error.error_detail`, {}))) {
            const errorDetail = _.get(rawFeatures, `[${featureId}].agentResult.${mainErrorType}.detail`, null);
            let agentParamName = _.get(systemMetadata, ['score_to_param_map', `${errorDetail}/${errorType}`], '');
            if (!agentParamName) {
              if (
                errorDetail === leadInspection2D &&
                [solderValidRatioList, padValidRatioList, tipValidRatioList].includes(errorType)
              ) {
                agentParamName = errorType;
              } else if (errorDetail === solderInspection2D && errorType === validRatioList) {
                agentParamName = errorType;
              }
            }
            if (!agentParamName) continue;
            // console.log('agentParamName', agentParamName);
            agentParamName = _.split(agentParamName, '/')[1];

            const featureObj = _.find(goldenFeatures, f => {
              return f.feature_id === Number(featureId) &&
              f.step === _.get(parsedComponents, `[${selectedRCid}].step`, 0) &&
              f.array_index === _.get(parsedComponents, `[${selectedRCid}].array_index`, null);
            });

            if (!featureObj) continue;

            let paramTypeAndValue;
            if (errorType === filletVolumeRatio) {
              // one result refers to three reference
              paramTypeAndValue = {
                type: filletVolumeRatio,
                filletUpperThreshold: _.get(featureObj, `line_item_params.${errorDetail}.params.${filletUpperThreshold}.param_float.value`, null),
                filletLowerThreshold: _.get(featureObj, `line_item_params.${errorDetail}.params.${filletLowerThreshold}.param_float.value`, null),
                filletOpenThreshold: _.get(featureObj, `line_item_params.${errorDetail}.params.${filletOpenThreshold}.param_float.value`, null),
                filletMinThreshold: _.get(featureObj, `line_item_params.${errorDetail}.params.${filletLowerThreshold}.param_float.min`, null),
                filletMaxThreshold: _.get(featureObj, `line_item_params.${errorDetail}.params.${filletUpperThreshold}.param_float.max`, null),
              };
            } else if (errorDetail === solderInspection2D && errorType === validRatioList) {
              paramTypeAndValue = {
                type: validRatioList,
                list: _.get(featureObj, `line_item_params.${errorDetail}.params.${solder2DValidRatioRanges}.param_vector`, []),
              };
            } else if (
              errorDetail === leadInspection2D &&
              [solderValidRatioList, tipValidRatioList].includes(errorType)
            ) {
              paramTypeAndValue = {
                type: errorType,
                mapValidRangeMap: {
                  solder: _.get(featureObj, `line_item_params.${errorDetail}.params.${solderValidRatioRange}.param_range`, {}),
                  liftedLead: _.get(featureObj, `line_item_params.${errorDetail}.params.${liftedLeadTipValidRatioRange}.param_range`, {}),
                },
              };
            } else {
              paramTypeAndValue = getAgentParamTypeNValueByParam(
                _.get(featureObj, `line_item_params.${errorDetail}.params.${agentParamName}`, {}),
                errorDetail,
                agentParamName,
              );
            }

            const rawInference = _.get(rawFeatures, `${featureId}.agentResult.${mainErrorType}.error.error_detail.${errorType}`, null);

            let inferenceResult = rawInference;
            let wrongPositions = [];
            let groupEquivalence = [];
            if (_.isObject(rawInference) && _.includes(_.keys(_.get(rawFeatures, `${featureId}.agentResult.${mainErrorType}.error.error_detail`)), 'predicted_text')) {
              const obj = _.get(rawFeatures, `${featureId}.agentResult.${mainErrorType}.error.error_detail`, {});
              inferenceResult = _.get(obj, 'predicted_text', '');
              paramTypeAndValue = {
                ...paramTypeAndValue,
                value: _.get(obj, 'expected_text', _.get(paramTypeAndValue, 'value')),
              };
              wrongPositions = _.get(obj, 'wrong_positions', []);
              groupEquivalence = _.get(obj, 'group_equivalence', []);
            }

            parsedError[`${errorType}`] = {
              reference: paramTypeAndValue,
              inferenceResult,
              referenceAgentParamName: agentParamName,
              detail: errorDetail,
              wrongPositions,
              groupEquivalence,
              isPredictedErrorType: errorType === mainErrorType,
            };
          }
        }

        newParsedFeatures.push({
          featureType: _.get(rawFeatures, `${featureId}.featureType`, ''),
          featureId: Number(featureId),
          parsedError: parsedError,
          mask,
        });
      }

      // console.log('newParsedFeatures', newParsedFeatures);

      setParsedFeatures(newParsedFeatures);
    };

    run(selectedRCid, parsedComponents, systemMetadata, goldenProductId, goldenFeatures);
  }, [parsedComponents, selectedRCid, systemMetadata, goldenFeatures]);

  return (
    <div className='flex py-4 flex-col flex-1 self-stretch gap-2'>
      <ConfigProvider
        theme={{
          components: {
            Collapse: {
              headerPadding: '0px 0 0px 8px',
              contentPadding: '0 0 0 8px',
            }
          }
        }}
      >
        <CustomCollapse
          style={{ width: '100%' }}
          activeKey={selectedFid}
          onChange={(keys) => {
            if (_.isEmpty(keys)) {
              setSelectedFid(null);
              setSelectedInspectionResultId(null);
              onMaskChange({ masks: [], selectedIndex: 0, show: false });
              return;
            }
            if (keys.length === 2) {
              const fid = Number(_.get(keys, '[1]', null));
              setSelectedFid(fid);
              setSelectedInspectionResultId(_.get(parsedComponents, `[${selectedRCid}].features[${fid}].inspectionResultId`, null));
              onMaskChange({ masks: [], selectedIndex: 0, show: false });
              return;
            }
            if (keys.length === 1) {
              const fid = Number(_.get(keys, '[0]', null));
              setSelectedFid(fid);
              setSelectedInspectionResultId(_.get(parsedComponents, `[${selectedRCid}].features[${fid}].inspectionResultId`, null));
              onMaskChange({ masks: [], selectedIndex: 0, show: false });
              return;
            }
          }}
          items={_.map(parsedFeatures, (f, idx) => (
            {
              key: String(f.featureId),
              label: <div className='flex h-[32px] px-2 items-center gap-2'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t(`featureTypeDisplayText.${_.startsWith(f.featureType, '_text') ? '_text' : f.featureType}`)}
                </span>
              </div>,
              children: <InferenceResult
                featureError={f}
                maskList={f.mask}
                onMaskChange={onMaskChange}
                maskInfo={maskInfo}
              />
            }
          ))}
        />
      </ConfigProvider>
    </div>
  );
};

export default Review;