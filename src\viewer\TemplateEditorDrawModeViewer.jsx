import React, { useCallback, useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import { fabric } from 'fabric';
import { useGetImageMetaDataQuery } from '../services/camera';
import { generalPan<PERSON>oom<PERSON>ouse<PERSON><PERSON><PERSON><PERSON><PERSON>, generalPan<PERSON><PERSON><PERSON>ouse<PERSON>oveHandler, generalPanZoomMouseUpHandler, generalPanZoomMouseWheelHandler, getAddLeadFeaturePayload, getAddMountingFeaturePayload, getAddSolderFeaturePayload, getAddTextVerificationFeaturePayload, getComponentRectInfoByFeatures, getRotatedRectBoundingBox, getTwoDRectPminPmax, initBarcodeScannerDefaultLineItemParams, initLeadDefaultLintItemParams, initMountingDefaultLineItemParams, initSolderDefaultLineItemParams, initTextVerificationDefaultLineItemParams, loadHighResolScene, loadInitFullSizeThumbnail, middlePanZoomMouseDownHandler, rotatePoint, zoomPanToObject } from './util';
import { useDispatch, useSelector } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled, setTransparentLoadingEnabled } from '../reducer/setting';
import { useTranslation } from 'react-i18next';
import {
  componentRoiPadding,
  featureROIInnerDimensionMaxLimit,
  highResoluRefreshInterval,
  newRectStrokeWidth,
  solderInspection3D,
  isAOI2DSMT,
  isAOI3DSMT,
} from '../common/const';
import { CustomSegmented } from '../common/styledComponent';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { useAddFeatureMutation, useAddFeatureRoiMutation, useUpdateComponentMutation } from '../services/product';
import { systemApi } from '../services/system';


const TemplateEditorDrawModeViewer = (props) => {
  const {
    curProduct,
    refetchAllComponents,
    refetchAllFeatures,
    updateAllFeaturesState,
    allFeatures,
    allComponents,
    selectedCid,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const viewerContRef = useRef(null);
  const canvasElRef = useRef(null);
  const fcanvasRef = useRef(null);
  const displayedHighResolSceneRef = useRef(null);
  const thumbnailBgSceneRef = useRef(null);
  const isPanningRef = useRef(false);
  const curDrawingRectRef = useRef(null);
  const drawingInitMousePos = useRef(null);
  const allFeatureRectsRef = useRef([]);
  const selectedFeatureTypeRef = useRef(null);

  const [drawingInfo, setDrawingInfo] = useState(null);
  const [selectedTool, setSelectedTool] = useState('transform');
  const [selectedFeatureType, setSelectedFeatureType] = useState(null);

  const { data: curImageMetadata } = useGetImageMetaDataQuery({ uri: _.get(curProduct, 'inspectables[0].color_map_uri') });
  const [addFeature] = useAddFeatureMutation();
  const [updateComponent] = useUpdateComponentMutation();
  const [addFeatureRoi] = useAddFeatureRoiMutation();

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  const delayLoadHighSoluScene = useCallback(
    _.debounce(async ({
      fcanvasRef,
      rawImageW,
      rawImageH,
      displayedHighResolSceneRef,
      imageUri,
      depthUri,
    }) => {
      dispatch(setTransparentLoadingEnabled(true));

      await loadHighResolScene({
        fcanvasRef,
        rawImageW,
        rawImageH,
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
        type: 'image',
        callback: () => {
          updateZIndex();
        },
      });

      dispatch(setTransparentLoadingEnabled(false));
    }, highResoluRefreshInterval),
  [curImageMetadata]);

  const handleToolChange = (tool) => {
    if (!fcanvasRef.current) return;

    fcanvasRef.current.discardActiveObject().renderAll();

    fcanvasRef.current.off('mouse:down');
    fcanvasRef.current.off('mouse:move');
    fcanvasRef.current.off('mouse:up');
    fcanvasRef.current.off('mouse:wheel');

    if (tool === 'transform') {
      fcanvasRef.current.on('mouse:down', (opt) => {
        opt.e.preventDefault();
        if (opt.e.button === 0) generalPanZoomMouseDownHandler(opt, fcanvasRef, isPanningRef);
      });
      fcanvasRef.current.on('mouse:move', (opt) => generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanningRef));
      fcanvasRef.current.on('mouse:up', (opt) => {
        if (opt.e.button === 0) {
          generalPanZoomMouseUpHandler(fcanvasRef, isPanningRef);
  
          const activeObj = fcanvasRef.current.getActiveObject();
          if (!_.isUndefined(activeObj) && !_.isNull(activeObj)) return;
  
          delayLoadHighSoluScene({
            fcanvasRef: fcanvasRef,
            rawImageW: _.get(curImageMetadata, 'width', 0),
            rawImageH: _.get(curImageMetadata, 'height', 0),
            displayedHighResolSceneRef,
            imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
            depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
          });
        }
      });
      fcanvasRef.current.on('mouse:wheel', (opt) => {
        generalPanZoomMouseWheelHandler(opt, fcanvasRef);
        delayLoadHighSoluScene({
          fcanvasRef,
          rawImageW: _.get(curImageMetadata, 'width', 0),
          rawImageH: _.get(curImageMetadata, 'height', 0),
          displayedHighResolSceneRef,
          imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
          depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
        });
      });
    } else if (tool === 'draw') {
      fcanvasRef.current.on('mouse:wheel', (opt) => {
        generalPanZoomMouseWheelHandler(opt, fcanvasRef);
        delayLoadHighSoluScene({
          fcanvasRef,
          rawImageW: _.get(curImageMetadata, 'width', 0),
          rawImageH: _.get(curImageMetadata, 'height', 0),
          displayedHighResolSceneRef,
          imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
          depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
        });
      });

      fcanvasRef.current.on('mouse:down', (opt) => {
        if (opt.e.button === 1) {
          middlePanZoomMouseDownHandler(fcanvasRef, isPanningRef);
        } else if (opt.e.button === 0) {
          const pointer = fcanvasRef.current.getPointer(opt.e);
          curDrawingRectRef.current = new fabric.Rect({
            left: pointer.x,
            top: pointer.y,
            width: 0,
            height: 0,
            fill: 'transparent',
            stroke: 'red',
            strokeWidth: newRectStrokeWidth,
            selectable: false,
            strokeUniform: true, // Ensure stroke width remains consistent when scaling
            evented: false,
          });

          const mousePos = fcanvasRef.current.getPointer(opt.e, true);

          setDrawingInfo({
            curRectInnerWidth: 0,
            curRectInnerHeight: 0,
            curMouseTop: mousePos.y,
            curMouseLeft: mousePos.x,
          });
          drawingInitMousePos.current = {
            x: pointer.x,
            y: pointer.y,
          };

          fcanvasRef.current.add(curDrawingRectRef.current);

          updateZIndex();
        }
      });

      fcanvasRef.current.on('mouse:move', (opt) => {
        if (isPanningRef.current) {
          generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanningRef);
          return;
        }

        if (!curDrawingRectRef.current || _.isEmpty(drawingInitMousePos.current)) return;
        const pointer = fcanvasRef.current.getPointer(opt.e);
        const mousePos = fcanvasRef.current.getPointer(opt.e, true);

        // NOTE: keep rect's width and height positive when drawing and adjust the top left
        // ow the rect's left and right will be hidden for some reason
        if (_.get(drawingInitMousePos.current, 'x') > pointer.x) {
          curDrawingRectRef.current.set({
            left: pointer.x,
            width: drawingInitMousePos.current.x - pointer.x,
          })
        } else {
          curDrawingRectRef.current.set({
            width: pointer.x - drawingInitMousePos.current.x,
            left: drawingInitMousePos.current.x,
          });
        }

        if (_.get(drawingInitMousePos.current, 'y') > pointer.y) {
          curDrawingRectRef.current.set({
            top: pointer.y,
            height: drawingInitMousePos.current.y - pointer.y,
          });
        } else {
          curDrawingRectRef.current.set({
            height: pointer.y - drawingInitMousePos.current.y,
            top: drawingInitMousePos.current.y,
          });
        }

        updateZIndex();

        const curInnerWidth = curDrawingRectRef.current.width - newRectStrokeWidth;
        const curInnerHeight = curDrawingRectRef.current.height - newRectStrokeWidth;

        setDrawingInfo({
          curRectInnerWidth: curInnerWidth >= 0 ? curInnerWidth : -curInnerWidth,
          curRectInnerHeight: curInnerHeight >= 0 ? curInnerHeight : -curInnerHeight,
          curMouseTop: mousePos.y,
          curMouseLeft: mousePos.x,
        });
      });

      fcanvasRef.current.on('mouse:up', () => {
        if (isPanningRef.current) {
          generalPanZoomMouseUpHandler(fcanvasRef, isPanningRef);
          delayLoadHighSoluScene({
            fcanvasRef: fcanvasRef,
            rawImageW: _.get(curImageMetadata, 'width', 0),
            rawImageH: _.get(curImageMetadata, 'height', 0),
            displayedHighResolSceneRef,
            imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
            depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
          });
          return;
        }

        if (curDrawingRectRef.current.width === 0 || curDrawingRectRef.current.height === 0) {
          fcanvasRef.current.remove(curDrawingRectRef.current);
          curDrawingRectRef.current = null;
          return;
        }

        if (!curDrawingRectRef.current) return;

        if (curDrawingRectRef.current.width < 0) {
          curDrawingRectRef.current.left += curDrawingRectRef.current.width;
          curDrawingRectRef.current.width *= -1;
        }
        if (curDrawingRectRef.current.height < 0) {
          curDrawingRectRef.current.top += curDrawingRectRef.current.height;
          curDrawingRectRef.current.height *= -1;
        }

        updateZIndex();
        curDrawingRectRef.current.setCoords();

        const { pMax, pMin } = getTwoDRectPminPmax(curDrawingRectRef.current, curDrawingRectRef.current.strokeWidth);

        fcanvasRef.current.remove(curDrawingRectRef.current);
        curDrawingRectRef.current = null;
        setDrawingInfo(null);
        drawingInitMousePos.current = null;

        if (pMax.x - pMin.x + 1 > featureROIInnerDimensionMaxLimit && pMax.y - pMin.y + 1 > featureROIInnerDimensionMaxLimit) {
          aoiAlert(t('notification.error.featureROIBoundaryLengthOverLimit'), ALERT_TYPES.COMMON_ERROR);
          return;
        }

        const selectedComponent = _.find(allComponents, c => c.region_group_id === selectedCid);

        if (!selectedComponent) return;

        const submit = async (pMin, pMax, systemMetadata, selectedComponent, curProduct, selectedFeatureType, allFeatures) => {
          let payload;

          if (selectedFeatureType === 'body') {
            payload = getAddMountingFeaturePayload({
              innerPMin: pMin,
              innerPMax: pMax,
              systemMetadata,
              productId: _.get(curProduct, 'product_id'),
              isAOI2DSMT,
              isAOI3DSMT,
            });
          }
          if (selectedFeatureType === 'solder') {
            payload = getAddSolderFeaturePayload({
              innerPMin: pMin,
              innerPMax: pMax,
              systemMetadata,
              productId: _.get(curProduct, 'product_id'),
            });
          }
          if (selectedFeatureType === 'ICLead') {
            payload = getAddLeadFeaturePayload({
              innerPMin: pMin,
              innerPMax: pMax,
              systemMetadata,
              productId: _.get(curProduct, 'product_id'),
              isAOI2DSMT,
              isAOI3DSMT,
            });
          }
          if (selectedFeatureType === 'text') {
            payload = getAddTextVerificationFeaturePayload({
              innerPMin: pMin,
              innerPMax: pMax,
              systemMetadata,
              productId: _.get(curProduct, 'product_id'),
            });
          }
          if (selectedFeatureType === 'barcode') {
            payload = getAddTextVerificationFeaturePayload({
              innerPMin: pMin,
              innerPMax: pMax,
              systemMetadata,
              productId: _.get(curProduct, 'product_id'),
            });
          }

          // add feature and update component(in group)
          const addFeatureRes = await addFeatureRoi({
            body: {
              ...payload,
              group_id: selectedComponent.region_group_id,
            },
            params: {
              allComponents: true,
            },
          });

          if (addFeatureRes.error) {
            aoiAlert(t('notification.error.addFeature'), ALERT_TYPES.COMMON_ERROR);
            console.error(addFeatureRes.error.message);
            return;
          }

          // regenerate the component pmin pmax
          let newComponentPminX = pMin.x - newRectStrokeWidth - componentRoiPadding.left;
          let newComponentPminY = pMin.y - newRectStrokeWidth - componentRoiPadding.top;
          let newComponentPmaxX = pMax.x + newRectStrokeWidth + componentRoiPadding.right;
          let newComponentPmaxY = pMax.y + newRectStrokeWidth + componentRoiPadding.bottom;

          const features = _.filter(allFeatures, f => f.group_id === _.get(selectedComponent, 'region_group_id', -1));

          // if (!_.isEmpty(features)) {
          //   for (const f of features) {
          //     newComponentPminX = Math.min(newComponentPminX, _.get(f, 'roi.points[0].x') - newRectStrokeWidth - componentRoiPadding.left);
          //     newComponentPminY = Math.min(newComponentPminY, _.get(f, 'roi.points[0].y') - newRectStrokeWidth - componentRoiPadding.top);
          //     newComponentPmaxX = Math.max(newComponentPmaxX, _.get(f, 'roi.points[1].x') + newRectStrokeWidth + componentRoiPadding.right);
          //     newComponentPmaxY = Math.max(newComponentPmaxY, _.get(f, 'roi.points[1].y') + newRectStrokeWidth + componentRoiPadding.bottom);
          //   }
          // }

          const newComponentInfo = getComponentRectInfoByFeatures(
            [
              ...features,
              { ...payload, group_id: selectedComponent.region_group_id },
            ],
            selectedComponent,
          );

          const cPayload = {
            ...selectedComponent,
            shape: {
              center: null,
              type: 'obb',
              points: [
                newComponentInfo.pMin,
                newComponentInfo.pMax,
              ],
              angle: 0,
            },
            all: true,
          };

          if (cPayload.feature_ids) delete cPayload['feature_ids'];
          
          delete cPayload['color_map_uri'];
          delete cPayload['depth_map_uri'];
          delete cPayload['created_at'];
          delete cPayload['modified_at'];
          delete cPayload['can_group_by_package_no'];
          delete cPayload['can_group_by_part_no'];
          delete cPayload['array_index'];
          delete cPayload['cloned'];
          delete cPayload['designator'];
          delete cPayload['variation_for'];
          delete cPayload['all'];

          const updateComponentRes = await updateComponent({ body: cPayload, params: { allComponents: true } });

          if (updateComponentRes.error) {
            aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
            console.error(updateComponentRes.error.message);
            return;
          }

          await refetchAllComponents();

          const addFeatureObjs = _.map(addFeatureRes.data, f => ({
            ...f,
            line_item_params: payload.line_item_params,
          }));

          await updateAllFeaturesState(_.map(addFeatureRes.data, 'feature_id'), 'add', addFeatureObjs);
        };

        submit(pMin, pMax, systemMetadata, selectedComponent, curProduct, selectedFeatureTypeRef.current, allFeatures);
      });
    }
  };

  const loadFeatures = (allFeatures, selectedCid, allComponents) => {
    if (!fcanvasRef.current) return;

    // remove all existing feature rects
    for (const f of allFeatureRectsRef.current) {
      fcanvasRef.current.remove(f);
    }
    allFeatureRectsRef.current = [];

    const selectedComponent = _.find(allComponents, c => c.region_group_id === selectedCid);
    if (!selectedComponent) return;

    for (const f of _.filter(allFeatures, f => f.group_id === selectedCid)) {
      const pMin = _.get(f, 'roi.points[0]', 0);
      const pMax = _.get(f, 'roi.points[1]', 0);

      const rect = new fabric.Rect({
        left: pMin.x - newRectStrokeWidth,
        top: pMin.y - newRectStrokeWidth,
        width: pMax.x - pMin.x + newRectStrokeWidth + 1,
        height: pMax.y - pMin.y + newRectStrokeWidth + 1,
        fill: 'transparent',
        stroke: 'white',
        strokeWidth: newRectStrokeWidth,
        strokeUniform: true,
        selectable: false,
        evented: false,
        angle: 0
      });

      rect.rotate(_.get(f, 'roi.angle', 0));

      fcanvasRef.current.add(rect);
      allFeatureRectsRef.current.push(rect);
    }

    const componentRect = new fabric.Rect({
      left: _.get(selectedComponent, 'shape.points[0].x', 0) - newRectStrokeWidth,
      top: _.get(selectedComponent, 'shape.points[0].y', 0) - newRectStrokeWidth,
      width: _.get(selectedComponent, 'shape.points[1].x', 0) - _.get(selectedComponent, 'shape.points[0].x', 0) + newRectStrokeWidth + 1,
      height: _.get(selectedComponent, 'shape.points[1].y', 0) - _.get(selectedComponent, 'shape.points[0].y', 0) + newRectStrokeWidth + 1,
      fill: 'transparent',
      stroke: 'white',
      strokeWidth: newRectStrokeWidth,
      strokeUniform: true,
      selectable: false,
      evented: false,
      angle: 0,
    });

    zoomPanToObject(componentRect, fcanvasRef.current);

    updateZIndex();

    delayLoadHighSoluScene({
      fcanvasRef: fcanvasRef,
      rawImageW: _.get(curImageMetadata, 'width', 0),
      rawImageH: _.get(curImageMetadata, 'height', 0),
      displayedHighResolSceneRef,
      imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
    });
  };

  const updateZIndex = () => {
    if (!fcanvasRef.current) return;
    
    if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.moveTo(1);
    if (displayedHighResolSceneRef.current) displayedHighResolSceneRef.current.moveTo(2);
    if (curDrawingRectRef.current) curDrawingRectRef.current.moveTo(3);
    if (allFeatureRectsRef.current.length > 0) {
      for (const f of allFeatureRectsRef.current) {
        f.moveTo(4);
      }
    }

    fcanvasRef.current.renderAll();
  };

  const init = async (curProduct, curImageMetadata, allFeatures, selectedCid, allComponents) => {
    await loadInitFullSizeThumbnail({
      fcanvas: fcanvasRef.current,
      rawWidth: _.get(curImageMetadata, 'width'),
      rawHeight: _.get(curImageMetadata, 'height'),
      thumbnailBgSceneRef,
      imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      type: 'image',
    });

    await loadHighResolScene({
      fcanvasRef,
      rawImageH: _.get(curImageMetadata, 'height', 0),
      rawImageW: _.get(curImageMetadata, 'width', 0),
      displayedHighResolSceneRef,
      imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      type: 'image',
    });

    loadFeatures(allFeatures, selectedCid, allComponents);

    updateZIndex();
  };

  useEffect(() => {
    if (!fcanvasRef.current) return;

    loadFeatures(allFeatures, selectedCid, allComponents);
    handleToolChange(selectedTool);
  }, [allFeatures, selectedCid, allComponents]);

  useEffect(() => {
    if (_.isUndefined(curImageMetadata) || _.isUndefined(curProduct) || !canvasElRef.current || !viewerContRef.current) return;

    if (!fcanvasRef.current) {
      fcanvasRef.current = new fabric.Canvas(canvasElRef.current, {
        antialias: 'off',
        uniformScaling: false,
        fireRightClick: true,
        stopContextMenu: true,
        preserveObjectStacking: true,
        fireMiddleClick: true,
      });
    }

    fcanvasRef.current.setHeight(viewerContRef.current.offsetHeight);
    fcanvasRef.current.setWidth(viewerContRef.current.offsetWidth);

    init(curProduct, curImageMetadata, allFeatures, selectedCid, allComponents);

    handleToolChange(selectedTool);
  }, [curImageMetadata]);

  useEffect(() => {
    const handleResize = () => {
      if (!fcanvasRef.current || !viewerContRef.current) return;

      fcanvasRef.current.setHeight(viewerContRef.current.offsetHeight);
      fcanvasRef.current.setWidth(viewerContRef.current.offsetWidth);
    };

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(viewerContRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div className='relative w-full h-full'>
      {!_.isEmpty(drawingInfo) &&
        <div
          className='absolute z-[22]'
          style={{
            display: drawingInfo.curRectInnerWidth === 0 && drawingInfo.curRectInnerHeight === 0 ? 'none' : 'block',
            top: `${drawingInfo.curMouseTop}px`,
            left: `${drawingInfo.curMouseLeft + 10}px`,
            borderRadius: '4px',
            background: '#56CCF2',
          }}
        >
          <div className='flex items-center gap-1 px-2'>
            <span
              className='font-source text-[12px] font-semibold'
              style={{ color: drawingInfo.curRectInnerWidth <= featureROIInnerDimensionMaxLimit ? '#131313' : '#EB5757' }}
            >
              {drawingInfo.curRectInnerWidth.toFixed(0)}
            </span>
            <span
              className='font-source text-[12px] font-semibold text-[#131313]'
            >x</span>
            <span
              className='font-source text-[12px] font-semibold'
              style={{ color: drawingInfo.curRectInnerHeight <= featureROIInnerDimensionMaxLimit ? '#131313' : '#EB5757' }}
            >
              {drawingInfo.curRectInnerHeight.toFixed(0)}
            </span>
          </div>
        </div>
      }
      {/* drawing info box ends */}
      {/* tools & controllers start */}
      <div
        className='absolute right-[8px] z-[21]'
        style={{ top: 'calc(50% - 117px)' }}
      >
        <CustomSegmented
          vertical
          value={_.isNull(selectedFeatureType) ? 'transform' : selectedFeatureType}
          onChange={(value) => {
            if (value === 'divider') return;
            switch (value) {
              case 'transform':
                setSelectedTool(value);
                setSelectedFeatureType(null);
                handleToolChange(value);
                selectedFeatureTypeRef.current = null;
                break;
              case 'body':
                setSelectedTool('draw');
                setSelectedFeatureType('body');
                handleToolChange('draw');
                selectedFeatureTypeRef.current = 'body';
                break;
              case 'solder':
                setSelectedTool('draw');
                setSelectedFeatureType('solder');
                handleToolChange('draw');
                selectedFeatureTypeRef.current = 'solder';
                break;
              case 'ICLead':
                setSelectedTool('draw');
                setSelectedFeatureType('ICLead');
                handleToolChange('draw');
                selectedFeatureTypeRef.current = 'ICLead';
                break; 
              case 'text':
                setSelectedTool('draw');
                setSelectedFeatureType('text');
                handleToolChange('draw');
                selectedFeatureTypeRef.current = 'text';
                break;
              case 'barcode':
                setSelectedTool('draw');
                setSelectedFeatureType('barcode');
                handleToolChange('draw');
                selectedFeatureTypeRef.current = 'barcode';
                break;
              default:
                break;
            }
          }}
          options={[
            {
              value: 'transform',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/backHand_white.svg'
                  alt='locator'
                  className='w-4 h-4'
                />
              </div>,
            },
            // {
            //   value: 'select3DView',
            //   label: <div className='flex w-8 h-8 justify-center items-center'>
            //     <img
            //       src='/icn/viewIn3D_white.svg'
            //       alt='viewIn3D'
            //       className='w-4 h-4'
            //     />
            //   </div>,
            // },
            // {
            //   value: 'divider',
            //   label: <div className='w-full h-[1px] bg-[#4F4F4F]' />,
            //   disabled: true,
            // },
            {
              value: 'body',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/addBody_color.svg'
                  alt='addBody'
                  className='w-6 h-6'
                />
              </div>,
            },
            {
              value: 'solder',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/addSolder_color.svg'
                  alt='addSolder'
                  className='w-6 h-6'
                />
              </div>,
            },
            {
              value: 'ICLead',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/addICLead_color.png'
                  alt='addICLead'
                  className='w-6 h-6'
                />
              </div>,
            },
            {
              value: 'text',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/addText_white.svg'
                  alt='addText'
                  className='w-6 h-[20px]'
                />
              </div>
            },
            {
              value: 'barcode',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/addBarcode_white.svg'
                  alt='addBarcode'
                  className='w-[20px] h-[20px]'
                />
              </div>,
            },
          ]}
        />
      </div>
      {/* tools & controllers end */}
      <div
        className='absolute top-0 left-0 w-full h-full z-[20]'
        ref={viewerContRef}
      >
        <canvas ref={canvasElRef} />
      </div>
    </div>
  );
};

export default TemplateEditorDrawModeViewer;