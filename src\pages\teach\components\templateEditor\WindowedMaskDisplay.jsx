import React from 'react';
import { Rnd } from 'react-rnd';
import InspectionReviewFeatureViewer from '../../../../viewer/InspectionReviewFeatureViewer';

const WindowedMaskDisplay = (props) => {
  const {
    id,
    selfUnmount,
    componentInfo,
    featureInfo,
    maskImage,
  } = props;

  return (
    <div className='fixed top-0 left-0 z-[30]'>
      <Rnd
        style={{ zIndex: 30 }}
        default={{
          width: 400,
          height: 400,
          x: (window.innerWidth - 400) / 2,
          y: (window.innerHeight - 400) / 2,
        }}
        bounds='window'
        dragHandleClassName='rnd-drag-handle'
      >
        <div
          className='flex flex-col w-full h-full self-stretch rounded-[6px]'
          style={{ boxShadow: '0px 4px 30px 15px rgba(255, 255, 255, 0.55)' }}
        >
          <div
            className='rnd-drag-handle flex items-center justify-center w-full h-[42px] bg-gray-1 rounded-t-[6px] self-stretch border-b-[1px] border-b-AOI-blue py-2 px-3 cursor-move relative'
          >
            <span className='font-source text-[12px] font-normal'>Mask</span>
            <div
              className='flex w-5 h-5 absolute top-[12.5px] right-[12px] hover:bg-[#eb575729] cursor-pointer items-center justify-center'
              onClick={() => selfUnmount(id)}
            >
              <img src='/icn/cross_white.svg' alt='cross' className='h-4 w-4' />
            </div>
          </div>
          <div className='flex w-full h-full self-stretch p-1 bg-[#1E1E1E] rounded-b-[6px]'>
            <div className='relative rounded-b-[6px] bg-gray-1 w-full h-full'>
              <InspectionReviewFeatureViewer
                componentInfo={componentInfo}
                featureInfo={featureInfo}
                isErroredSample={false}
                isInspectedView={true}
                maskImage={maskImage}
                isDepthMapDisplayed={false}
              />
            </div>
          </div>
        </div>
      </Rnd>
    </div>
  );
};

export default WindowedMaskDisplay;
