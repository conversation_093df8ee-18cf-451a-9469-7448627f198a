import { ConfigProvider } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { CustomCollapse, CustomFeatureCollapse } from '../../../../../common/styledComponent';
import _ from 'lodash';
import { useTranslation } from 'react-i18next';
import { leadFeatureType, leadGapFeatureType, textVerification } from '../../../../../common/const';


const GroupByPackageNo = (props) => {
  const {
    filteredComponents,
    allFeatures,
    selectedPackageNo,
    setSelectedPackageNo,
    allPackagesCollapseRef,
    selectedFeatureType,
    setSelectedFeatureType,
    setSelectedPartNo,
    setSelectedCid,
    setRequiredLocateRect,
    setSelectedScope,
    setSelectedFid,
    setSelectedAgentParam,
    setSelectedUngroupedFid,
    selectedUngroupedFid,
    allFeatureReevaluationResult,
    selectedCid,
    selectedPartNo,
    selectedArrayIndex,
    setSelectedArrayIndex,
    aggregatedReevaluationResult,
    setShouldTrainingSetRefetch,
  } = props;

  const { t } = useTranslation();

  const [parsedMap, setParsedMap] = useState({}); // { 'ex. ${packageNo} / ${partNo} / ${cid}': { label: '${packageNo} / ${partNo} / ${designator}', scope: 'package|part|component', featureTypesMap: {...} } }
  const [selectedLabel, setSelectedLabel] = useState(null);
  const [labelFeatureTypeToPassingInfo, setLabelFeatureTypeToPassingInfo] = useState({});
  const [labelToPassingInfo, setLabelToPassingInfo] = useState({}); // {label: {num_passing, num_failing, contains_white_feature}}
  const [parsedAggregateResult, setParsedAggregateResult] = useState({});
  const [cidToFeatureTypeLineItemParamsMap, setCidToFeatureTypeLineItemParamsMap] = useState({});

  useEffect(() => {
    if (_.isInteger(selectedUngroupedFid)) setSelectedLabel(null);
  }, [selectedUngroupedFid]);

  useEffect(() => {
    if (_.isEmpty(aggregatedReevaluationResult)) {
      setParsedAggregateResult({});
      return;
    }

    const newMap = {};
    for (const r of aggregatedReevaluationResult) {
      if (!_.isEmpty(r.package_no)) {
        newMap[`${r.package_no}`] = r;
      }
      if (!_.isEmpty(r.part_no)) {
        newMap[`${r.part_no}`] = r;
      }
      newMap[`${r.region_group_id}`] = r;
    }

    setParsedAggregateResult(newMap);
  }, [aggregatedReevaluationResult]);

  useEffect(() => {
    // all component group by list higharchy is group by item -> feature type
    let newMap = {};
    let cidToFeatureTypeLineItemParamsMap = {}; // cid -> feature type -> line item params

    // same as group by part no first get each component's line item params config
    for (const f of allFeatures) {
      if (_.isInteger(f.group_id)) {
        if (!_.has(cidToFeatureTypeLineItemParamsMap, `${f.group_id}`)) {
          // if (!_.startsWith(f.feature_type, '_text')) {
          //   cidToFeatureTypeLineItemParamsMap[`${f.group_id}`] = {
          //     [`${f.feature_type}`]: {
          //       ...f.line_item_params,
          //       sample_fid: f.feature_id,
          //     } || {},
          //   };
          // } else {
          //   cidToFeatureTypeLineItemParamsMap[`${f.group_id}`] = {
          //     [`_text`]: {
          //       ...f.line_item_params,
          //       sample_fid: f.feature_id,
          //     } || {},
          //   };
          // }
          cidToFeatureTypeLineItemParamsMap[`${f.group_id}`] = {
            [`${f.feature_type}`]: {
              ...f.line_item_params,
              sample_fid: f.feature_id,
            } || {},
          };
        } else {
          if (_.includes(_.keys(cidToFeatureTypeLineItemParamsMap[`${f.group_id}`]), f.feature_type)) continue;
          // if (!_.startsWith(f.feature_type, '_text')) {
          //   cidToFeatureTypeLineItemParamsMap[`${f.group_id}`][`${f.feature_type}`] = {
          //     ...f.line_item_params,
          //     sample_fid: f.feature_id,
          //   } || {};
          // } else {
          //   cidToFeatureTypeLineItemParamsMap[`${f.group_id}`]['_text'] = {
          //     ...f.line_item_params,
          //     sample_fid: f.feature_id,
          //   } || {};
          // }
          cidToFeatureTypeLineItemParamsMap[`${f.group_id}`][`${f.feature_type}`] = {
            ...f.line_item_params,
            sample_fid: f.feature_id,
          } || {};
        }
      }
    }

    setCidToFeatureTypeLineItemParamsMap(cidToFeatureTypeLineItemParamsMap);

    // console.log('cidToFeatureTypeLineItemParamsMap', cidToFeatureTypeLineItemParamsMap);

    for (const c of filteredComponents) {
      if (!_.has(cidToFeatureTypeLineItemParamsMap, String(c.region_group_id))) continue;
      if (!_.isEmpty(c.package_no) && c.can_group_by_package_no === true && c.can_group_by_part_no === true) {
        // package no group defined and linked to the part no group
        if (_.has(newMap, [c.package_no])) {
          newMap = _.set(newMap, [c.package_no], {
            ...newMap[`${c.package_no}`],
            healthy: c.healthy,
            featureTypesMap: {
              ..._.get(newMap, [c.package_no, 'featureTypesMap'], {}),
              ...cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
            },
          });
        } else {
          newMap[`${c.package_no}`] = {
            scope: 'package',
            packageNo: c.package_no,
            featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
            healthy: c.healthy,
            cid: c.region_group_id,
            partNo: c.part_no,
            arrayIndex: c.array_index,
          };
        }
      } else if (!_.isEmpty(c.package_no)) {
        // package no defined but not linked to the package no group
        if (c.can_group_by_part_no === false && c.can_group_by_package_no === false) {
          const key = `${c.package_no} / ${_.isEmpty(c.part_no) ? t('common.unknownPartNo') : c.part_no} / ${c.region_group_id}`;
          if (!_.has(newMap, key)) {
            newMap[key] = {
              label: `${c.package_no} / ${_.isEmpty(c.part_no) ? t('common.unknownPartNo') : c.part_no} / ${c.designator}`,
              scope: 'component',
              cid: c.region_group_id,
              featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
              healthy: c.healthy,
              packageNo: c.package_no,
              partNo: c.part_no,
              arrayIndex: c.array_index,
            };
          } else {
            newMap = _.set(newMap, [key, 'featureTypesMap'], {
              ..._.get(newMap, [key, 'featureTypesMap'], {}),
              ...cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
            });
            newMap = _.set(newMap, [key, 'healthy'], c.healthy);
          }
        } else if (c.can_group_by_part_no !== true) {
          if (_.isEmpty(c.part_no)) {
            if (!_.has(newMap, [c.package_no])) {
              newMap[`${c.package_no}`] = {
                scope: 'package',
                packageNo: c.package_no,
                featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
                healthy: c.healthy,
                cid: c.region_group_id,
                partNo: c.part_no,
                arrayIndex: c.array_index,
              };
            } else {
              newMap = _.set(newMap, [c.package_no, 'featureTypesMap'], {
                ..._.get(newMap, [c.package_no, 'featureTypesMap'], {}),
                ...cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
              });
              newMap = _.set(newMap, [c.package_no, 'healthy'], c.healthy);
            }
          } else {
            // not linked to the part no group
            newMap[`${c.package_no} / ${_.isEmpty(c.part_no) ? t('common.unknownPartNo') : c.part_no} / ${c.region_group_id}`] = {
              label: `${c.package_no} / ${_.isEmpty(c.part_no) ? t('common.unknownPartNo') : c.part_no} / ${c.designator}`,
              scope: 'component',
              cid: c.region_group_id,
              featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
              healthy: c.healthy,
              packageNo: c.package_no,
              partNo: c.part_no,
              arrayIndex: c.array_index,
            };
          }
        } else if (c.can_group_by_part_no === true && c.can_group_by_package_no === false) {
          // linked to the part no group but not linked to the package no group
          if (_.isEmpty(c.part_no)) {
            newMap[`${c.package_no} / ${t('common.unknownPartNo')} / ${c.region_group_id}`] = {
              label: `${c.package_no} / ${t('common.unknownPartNo')} / ${c.designator}`,
              scope: 'component',
              cid: c.region_group_id,
              featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
              healthy: c.healthy,
              packageNo: c.package_no,
              partNo: c.part_no,
              arrayIndex: c.array_index,
            };
          } else {
            if (!_.has(newMap, [`${c.package_no} / ${c.part_no}`])) {
              newMap[`${c.package_no} / ${c.part_no}`] = {
                scope: 'part',
                partNo: c.part_no,
                featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
                healthy: c.healthy,
                packageNo: c.package_no,
                cid: c.region_group_id,
                arrayIndex: c.array_index,
              };
            } else {
              newMap = _.set(newMap, [`${c.package_no} / ${c.part_no}`, 'featureTypesMap'], {
                ..._.get(newMap, [`${c.package_no} / ${c.part_no}`, 'featureTypesMap'], {}),
                ...cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
              });
              newMap = _.set(newMap, [`${c.package_no} / ${c.part_no}`, 'healthy'], c.healthy);
            }
          }
        }
      } else if (_.isEmpty(c.package_no)) {
        // package no not defined
        if (!_.isEmpty(c.part_no) && c.can_group_by_part_no === true) {
          // part no group defined and linked to the part no group
          if (!_.has(newMap, [`${t('common.unknownPackageNo')} / ${c.part_no}`])) {
            newMap[`${t('common.unknownPackageNo')} / ${c.part_no}`] = {
              scope: 'part',
              partNo: c.part_no,
              featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
              healthy: c.healthy,
              packageNo: c.package_no,
              cid: c.region_group_id,
              arrayIndex: c.array_index,
            };
          } else {
            newMap[`${t('common.unknownPackageNo')} / ${c.part_no}`].featureTypesMap = {
              ..._.get(newMap, [`${t('common.unknownPackageNo')} / ${c.part_no}`, 'featureTypesMap'], {}),
              ...cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
            };
            newMap = _.set(newMap, `${t('common.unknownPackageNo')} / ${c.part_no}.healthy`, c.healthy);
          }
        } else {
          // scope is component but label still depends on the part no is defined or not
          newMap[`${t('common.unknownPackageNo')} / ${_.isEmpty(c.part_no) ? t('common.unknownPartNo') : c.part_no} / ${c.region_group_id}`] = {
            label: `${t('common.unknownPackageNo')} / ${_.isEmpty(c.part_no) ? t('common.unknownPartNo') : c.part_no} / ${c.designator}`,
            scope: 'component',
            cid: c.region_group_id,
            featureTypesMap: cidToFeatureTypeLineItemParamsMap[`${c.region_group_id}`],
            healthy: c.healthy,
            partNo: c.part_no,
            packageNo: c.package_no,
            arrayIndex: c.array_index,
          };
        }
      }
    }

    // console.log('newMap', newMap);
    setParsedMap(newMap);
  }, [
    filteredComponents,
    allFeatures,
  ]);

  useEffect(() => {
    const fidToFeatureMap = _.keyBy(allFeatures, f => _.get(f, 'feature_id', 0));
    const cidToComponentMap = _.keyBy(filteredComponents, c => _.get(c, 'region_group_id', 0));
    const newMap = {};
    const labelMap = {}; // {label: {num_passing, num_failing, contains_white_feature}}

    // console.log('allFeatureReevaluationResult', allFeatureReevaluationResult);

    for (const featureInferenceResult of allFeatureReevaluationResult || []) {
      const fid = _.get(featureInferenceResult, 'feature_id', 0);
      const featureObj = fidToFeatureMap[fid];
      if (!featureObj || !_.isInteger(featureObj.group_id)) continue;
      const componentObj = cidToComponentMap[featureObj.group_id];
      if (!componentObj) continue;

      let label;
      if (!_.isEmpty(componentObj.package_no) && componentObj.can_group_by_package_no === true && componentObj.can_group_by_part_no === true) {
        label = `${componentObj.package_no}`;
      } else if (!_.isEmpty(componentObj.package_no)) {
        if (componentObj.can_group_by_package_no === false && componentObj.can_group_by_part_no === false) {
          label = `${componentObj.package_no} / ${_.isEmpty(componentObj.part_no) ? t('common.unknownPartNo') : componentObj.part_no} / ${componentObj.region_group_id}`;
        } else if (componentObj.can_group_by_part_no === false) {
          if (_.isEmpty(componentObj.part_no)) {
            label = `${componentObj.package_no}`;
          } else {
            label = `${componentObj.package_no} / ${componentObj.part_no} / ${componentObj.region_group_id}`;
          }
        } else if (componentObj.can_group_by_part_no === true && componentObj.can_group_by_package_no === false) {
          if (_.isEmpty(componentObj.part_no)) {
            label = `${componentObj.package_no} / ${t('common.unknownPartNo')} / ${componentObj.region_group_id}`;
          } else {
            label = `${componentObj.package_no} / ${componentObj.part_no}`;
          }
        }
        // if (componentObj.can_group_by_part_no !== true) {
        //   label = `${componentObj.package_no} / ${_.isEmpty(componentObj.part_no) ? t('common.unknownPartNo') : componentObj.part_no} / ${componentObj.designator}`;
        // } else if (componentObj.can_group_by_package_no === false) {
        //   if (_.isEmpty(componentObj.part_no)) {
        //     label = `${componentObj.package_no} / ${t('common.unknownPartNo')} / ${componentObj.designator}`;
        //   } else {
        //     label = `${componentObj.package_no} / ${componentObj.part_no}`;
        //   }
        // } else {
        //   label = `${componentObj.package_no}`;
        // }
      } else {
        if (!_.isEmpty(componentObj.part_no) && componentObj.can_group_by_part_no === true) {
          label = `${t('common.unknownPackageNo')} / ${componentObj.part_no}`;
        } else {
          label = `${t('common.unknownPackageNo')} / ${_.isEmpty(componentObj.part_no) ? t('common.unknownPartNo') : componentObj.part_no} / ${componentObj.region_group_id}`;
        }
      }

      const featureType = featureObj.feature_type;
      const key = `${label}_${featureType}`;
      if (!_.has(newMap, key)) {
        newMap[key] = { num_passing: 0, num_failing: 0 };
      }
      const numPassing = _.get(featureInferenceResult, 'num_passing', 0);
      const numFailing = _.get(featureInferenceResult, 'num_failing', 0);
      newMap[key].num_passing += numPassing;
      newMap[key].num_failing += numFailing;
      if (!_.has(labelMap, label)) {
        labelMap[label] = { num_passing: 0, num_failing: 0, contains_white_feature: false };
      }
      labelMap[label].num_passing += numPassing;
      labelMap[label].num_failing += numFailing;
      if (numFailing === 0 && numPassing === 0) {
        labelMap[label].contains_white_feature = true;
      }
    }

    setLabelFeatureTypeToPassingInfo(newMap);
    setLabelToPassingInfo(labelMap);
  }, [allFeatureReevaluationResult, allFeatures, filteredComponents, t]);

  useEffect(() => {
    if (!_.isInteger(selectedCid)) return;

    const c = _.find(filteredComponents, c => c.region_group_id === selectedCid && c.array_index === selectedArrayIndex);
    if (!c) return;

    let label;
    if (!_.isEmpty(selectedPackageNo) && c.can_group_by_package_no === true) {
      label = `${selectedPackageNo}`;
      setSelectedScope('package');
    } else if (c.can_group_by_part_no === true && !_.isEmpty(selectedPartNo)) {
      label = `${_.isEmpty(selectedPackageNo) ? t('common.unknownPackageNo') : selectedPackageNo} / ${selectedPartNo}`;
      setSelectedScope('part');
    } else {
      label = `${_.isEmpty(selectedPackageNo) ? t('common.unknownPackageNo') : selectedPackageNo} / ${_.isEmpty(selectedPartNo) ? t('common.unknownPartNo') : selectedPartNo} / ${c.region_group_id}`;
      setSelectedScope('component');
    }
    setSelectedLabel(label);
  }, [
    selectedPackageNo,
    selectedPartNo,
    selectedCid,
  ]);

  return (
    <Fragment>
      <ConfigProvider
        theme={{
          components: {
            Collapse: {
              headerPadding: '0px 0 0px 8px',
              contentPadding: '0 0 0 8px',
            }
          }
        }}
      >
        <CustomCollapse
          ref={allPackagesCollapseRef}
          style={{ width: '100%' }}
          onChange={(keys) => {
            let label;
            setSelectedFeatureType(null);
            setSelectedFid(null);
            setSelectedUngroupedFid(null);
            setSelectedAgentParam(null);

            if (_.isEmpty(keys)) {
              setSelectedPartNo(null);
              setSelectedCid(null);
              setSelectedLabel(null);
              setSelectedScope(null);
            } else if (keys.length === 1) {
              label = keys[0];
            } else {
              label = keys[1];
            }

            // console.log('label', label);
            // console.log('parsedMap', parsedMap);

            if (label) {
              // console.log(_.get(parsedMap, [label], {}));
              setSelectedLabel(label);
              setSelectedScope(_.get(parsedMap, [label, 'scope'], null));
              if (_.get(parsedMap, [label, 'scope']) === 'package') {
                setSelectedCid(_.get(parsedMap, [label, 'cid'], null));
                setSelectedPartNo(null);
                setSelectedPackageNo(_.get(parsedMap, [label, 'packageNo'], null));
                setRequiredLocateRect({
                  cid: _.get(parsedMap, [label, 'cid'], null),
                  fid: null,
                });
                // setSelectedArrayIndex(_.get(parsedMap, [label, 'arrayIndex'], null));
                if (_.isInteger(_.get(parsedMap, [label, 'arrayIndex'], null))) {
                  setSelectedArrayIndex(0);
                } else {
                  setSelectedArrayIndex(null);
                }
              } else if (_.get(parsedMap, [label, 'scope']) === 'part') {
                setSelectedCid(_.get(parsedMap, [label, 'cid'], null));
                setSelectedPartNo(_.get(parsedMap, [label, 'partNo'], null));
                setSelectedPackageNo(null);
                setRequiredLocateRect({
                  cid: _.get(parsedMap, [label, 'cid'], null),
                  fid: null,
                });
                // setSelectedArrayIndex(_.get(parsedMap, [label, 'arrayIndex'], null));
                if (_.isInteger(_.get(parsedMap, [label, 'arrayIndex'], null))) {
                  setSelectedArrayIndex(0);
                } else {
                  setSelectedArrayIndex(null);
                }
              } else if (_.get(parsedMap, [label, 'scope']) === 'component') {
                !_.isEmpty(_.get(parsedMap, [label, 'partNo'], null)) ? setSelectedPartNo(_.get(parsedMap, [label, 'partNo'], null)) : setSelectedPartNo(null);
                !_.isEmpty(_.get(parsedMap, [label, 'packageNo'], null)) ? setSelectedPackageNo(_.get(parsedMap, [label, 'packageNo'], null)) : setSelectedPackageNo(null);
                setSelectedCid(_.get(parsedMap, [label, 'cid'], null));
                setRequiredLocateRect({
                  cid: _.get(parsedMap, [label, 'cid'], null),
                  fid: null,
                });
                // setSelectedArrayIndex(_.get(parsedMap, [label, 'arrayIndex'], null));
                if (_.isInteger(_.get(parsedMap, [label, 'arrayIndex'], null))) {
                  setSelectedArrayIndex(0);
                } else {
                  setSelectedArrayIndex(null);
                }
              }
            }
          }}
          activeKey={_.isNull(selectedLabel) ? [] : [String(selectedLabel)]}
          items={_.map(_.keys(parsedMap), (labelKey) => {
            const packageNoGroupObj = _.get(parsedMap, [labelKey], {});
            const displayLabel = _.get(packageNoGroupObj, 'label', labelKey);
            return {
              key: labelKey,
              label: <div className={`flex h-[32px] px-2 items-center gap-2 justify-between`}>
                {(() => {
                  const isHealthy = _.get(packageNoGroupObj, 'healthy', false);
                  if (!isHealthy) {
                    return (
                      <Fragment>
                        <div className='flex items-center gap-1'>
                          <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}>
                            {displayLabel}
                          </span>
                        </div>
                        <div
                          className='flex flex-col justify-center items-center gap-2.5 shrink-0 cursor-pointer'
                          title={t('productDefine.confirmComponentInfo')}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleConfirmUnhealthyPackageNoGroup(packageNoGroupObj);
                          }}
                        >
                          <img
                            src='/icn/unknown_gray.svg'
                            className='w-[12px] h-[12px]'
                            alt='unknown'
                          />
                        </div>
                      </Fragment>
                    );
                  }

                  const numFail = _.get(labelToPassingInfo, [labelKey, 'num_failing'], 0);
                  const numPass = _.get(labelToPassingInfo, [labelKey, 'num_passing'], 0);
                  const containsWhite = _.get(labelToPassingInfo, [labelKey, 'contains_white_feature'], false);
                  if (numFail === 0 && containsWhite && numPass > 0) {
                    return (
                      <div className='flex items-center gap-1'>
                        <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}>
                          {displayLabel}
                        </span>
                      </div>
                    );
                  }

                  if (numFail > 0) {
                    return (
                      <Fragment>
                        <div className='flex items-center gap-1'>
                          <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap text-[#EB5E28]`}>
                          {displayLabel}
                          </span>
                        </div>
                        <img src={'/icn/failedCircled_red.svg'} className='w-[12px] h-[12px]' alt='status' />
                      </Fragment>
                    );
                  }

                  if (numFail === 0 && !containsWhite && numPass > 0) {
                    return (
                      <Fragment>
                        <div className='flex items-center gap-1'>
                          <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap text-[#57F2C4]`}>
                          {displayLabel}
                          </span>
                        </div>
                        <img src={'/icn/checkFilledCircle_green.svg'} className='w-[12px] h-[12px]' alt='status' />
                      </Fragment>
                    );
                  }

                  return (
                    <div className='flex items-center gap-1'>
                      <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}>
                        {displayLabel}
                      </span>
                    </div>
                  );
                })()}
              </div>,
              children:
                <ConfigProvider
                  theme={{
                    components: {
                      Collapse: {
                        headerPadding: '0 0 0 8px',
                        contentPadding: '0 0 0 8px',
                      }
                    }
                  }}
                >
                  <CustomFeatureCollapse
                    style={{ width: '100%' }}
                    onChange={(keys) => {
                      setSelectedUngroupedFid(null);
                      setSelectedAgentParam(null);

                      let featureType;

                      if (_.isEmpty(keys)) {
                        setSelectedFeatureType(null);
                      } else if (keys.length === 1) {
                        setSelectedFeatureType(keys[0]);
                        featureType = keys[0];
                      } else {
                        setSelectedFeatureType(keys[1]);
                        featureType = keys[1];
                      }

                      featureType = _.startsWith(featureType, '_ic_lead') ? '_ic_lead' : featureType;

                      let sampleFid;

                      if (_.isInteger(selectedCid)) {
                        if (_.startsWith(featureType, '_text')) {
                          const feature = _.find(allFeatures, f => f.feature_type === featureType && f.array_index === selectedArrayIndex);

                          if (!feature) return;

                          setSelectedCid(feature.group_id);
                          setSelectedFid(feature.feature_id);
                          setRequiredLocateRect({
                            cid: feature.group_id,
                            fid: feature.feature_id,
                          });

                          setShouldTrainingSetRefetch(true);
                          return;
                        }
                        sampleFid = _.get(
                          _.find(allFeatures, f => f.feature_type === featureType && f.group_id === selectedCid && f.array_index === selectedArrayIndex),
                          'feature_id',
                        );
                        setSelectedFid(sampleFid);
                        setRequiredLocateRect({
                          cid: selectedCid,
                          fid: sampleFid,
                        });
                      }
                    }}
                    expandIcon={() => null}
                    activeKey={_.isNull(selectedFeatureType) ? [] : [selectedFeatureType]}
                    // activeKey={activeKey}
                    items={_.reduce(
                      _.keys(_.get(packageNoGroupObj, 'featureTypesMap', {})),
                      (acc, type) => {
                        let numFail;
                        let numPass;

                        if (!_.includes([leadFeatureType, leadGapFeatureType], type)) {
                          numFail = _.get(labelFeatureTypeToPassingInfo, [`${labelKey}_${type}`, 'num_failing'], 0);
                          numPass = _.get(labelFeatureTypeToPassingInfo, [`${labelKey}_${type}`, 'num_passing'], 0);
                        } else {
                          const key = _.get(packageNoGroupObj, 'scope') === 'package' ? _.get(packageNoGroupObj, 'packageNo') : _.get(packageNoGroupObj, 'scope') === 'part' ? _.get(packageNoGroupObj, 'partNo') : _.get(packageNoGroupObj, 'cid');
                          numFail = _.get(parsedAggregateResult, [key, 'feature_type_aggregates', `${type === leadFeatureType ? '_ic_lead' : '_ic_lead_gap'}`, 'num_failing'], 0);
                          numPass = _.get(parsedAggregateResult, [key, 'feature_type_aggregates', `${type === leadFeatureType ? '_ic_lead' : '_ic_lead_gap'}`, 'num_passing'], 0);
                        }

                        const color = numFail > 0 ? '#EB5E28' : numPass > 0 ? '#57F2C4' : '#fff';
                        const icon = numFail > 0 ? '/icn/failedCircled_red.svg' : numPass > 0 ? '/icn/checkFilledCircle_green.svg' : '';
                        if (type === leadFeatureType) {
                          return acc.concat([
                            {
                              key: leadFeatureType,
                              label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                                <span
                                  className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}
                                  style={{ color }}
                                >
                                  {t('leadFeatureTypeText._ic_lead')}
                                </span>
                                {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                              </div>,
                            },
                            {
                              key: leadGapFeatureType,
                              label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                                <span
                                  className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`}
                                  style={{ color }}
                                >
                                  {t('leadFeatureTypeText._ic_lead_gap')}
                                </span>
                                {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                              </div>,
                            }
                          ]);
                        }

                        if (_.startsWith(type, '_text')) {
                          return acc.concat([{
                            key: type,
                            label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                              {(() => {
                                const numFail = _.get(labelFeatureTypeToPassingInfo, [`${labelKey}_${type}`, 'num_failing'], 0);
                                const numPass = _.get(labelFeatureTypeToPassingInfo, [`${labelKey}_${type}`, 'num_passing'], 0);
                                const color = numFail > 0 ? '#EB5E28' : numPass > 0 ? '#57F2C4' : '#fff';
                                const icon = numFail > 0 ? '/icn/failedCircled_red.svg' : numPass > 0 ? '/icn/checkFilledCircle_green.svg' : '';

                                return (
                                  <Fragment>
                                    <div className='flex items-center gap-2'>
                                      <span className={`font-source text-[12px] font-normal leading-[150%]`} style={{ color }}>
                                        {t('leadFeatureTypeText._text')}
                                      </span>
                                      {/* also show the expected text from line item params */}
                                      <span
                                        className='font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap'
                                        style={{ color }}
                                        title={_.get(packageNoGroupObj, [
                                          'featureTypesMap',
                                          type,
                                          textVerification,
                                          'params',
                                          'expected_text',
                                          'param_string',
                                        ], '')}
                                      >
                                        ({_.get(packageNoGroupObj, [
                                          'featureTypesMap',
                                          type,
                                          textVerification,
                                          'params',
                                          'expected_text',
                                          'param_string',
                                        ], '')})
                                      </span>
                                    </div>
                                    {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                                  </Fragment>
                                );
                              })()}
                            </div>,
                          }]);
                        }

                        return acc.concat([{
                          key: type,
                          label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
                            {(() => {
                              const numFail = _.get(labelFeatureTypeToPassingInfo, [`${labelKey}_${type}`, 'num_failing'], 0);
                              const numPass = _.get(labelFeatureTypeToPassingInfo, [`${labelKey}_${type}`, 'num_passing'], 0);
                              const color = numFail > 0 ? '#EB5E28' : numPass > 0 ? '#57F2C4' : '#fff';
                              const icon = numFail > 0 ? '/icn/failedCircled_red.svg' : numPass > 0 ? '/icn/checkFilledCircle_green.svg' : '';
                              return (
                                <Fragment>
                                  <div className='flex items-center gap-1'>
                                    <span className={`font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap`} style={{ color }}>
                                      {t(`leadFeatureTypeText.${type.startsWith('_text') ? '_text': type}`)}
                                    </span>
                                  </div>
                                  {icon && <img src={icon} className='w-[12px] h-[12px]' alt='status' />}
                                </Fragment>
                              );
                            })()}
                          </div>,
                        }]);
                      },
                      [],
                    )}
                  />
                </ConfigProvider>
            };
          })}
        />
      </ConfigProvider>
    </Fragment>
  );
};

export default GroupByPackageNo;