import React, { useState } from 'react';
import Team from './Team';
import { useTranslation } from 'react-i18next';
import { Menu } from 'antd';
import Language from './SystemSettings/Language';
import SystemConfig from './SystemSettings/SystemConfig';
import Host from './SystemSettings/Host';
import { LogoutOutlined } from '@ant-design/icons';
import { localStorageKeys, userRoles } from '../../common/const';
import _ from 'lodash';


const Settings = () => {
	const { t } = useTranslation();

	const [selectedTab, setSelectedTab] = useState('host'); // system, team

	const handleLogout = () => {
		localStorage.removeItem(localStorageKeys.accessToken);
		localStorage.removeItem(localStorageKeys.tokenExp);

		window.location.href = '/login';
		return;
	};

	const items = [
		{
			key: 'team',
			label: (
				<div className="flex items-center gap-2">
					<img
						src="/icn/team_white.svg"
						alt="team"
						className="h-[12px] w-[11px]"
					/>
					<span
						className={`font-source text-[14px] leading-[normal] ${
							selectedTab === 'team' ? 'font-semibold' : 'font-normal'
						}`}
					>
						{t('settings.team')}
					</span>
				</div>
			),
			disabled: localStorage.getItem(localStorageKeys.userRole) !== userRoles.admin,
		},
		{
			key: 'system',
			label: (
				<div className="flex items-center gap-2">
					<img
						src="/icn/gear_white.svg"
						alt="gear"
						className="h-[11.3px] w-[12px]"
					/>
					<span
						className={`font-source text-[14px] leading-[normal] text-[color:var(--daoai-white,#FFF)] ${
							selectedTab === 'system' ? 'font-semibold' : 'font-normal'
						}`}
					>
						{t('settings.systemSettings')}
					</span>
				</div>
			),
			// type: 'group',
			children: [
				{ key: 'language', label: t('settings.language') },
				{ key: 'host', label: t('settings.host') },
				{
					key: 'system_config',
					label: t('settings.systemConfig'),
					disabled: !_.includes([userRoles.admin, userRoles.programmer], localStorage.getItem(localStorageKeys.userRole)),
				},
			],
		},
		{
			key: 'logout',
			label: (
				<div className='flex items-center gap-2'>
					<LogoutOutlined
						style={{ width: '8px', height: '12px' }}
					/>
					<span
						className='font-source text-[14px] leading-[normal]'
						style={{ marginInlineStart: '0' }}
					>
						{t('common.logout')}
					</span>
				</div>
			),
			children: null
		},
	];

	return (
		<div className="flex items-center flex-1 self-stretch">
			<div className="flex w-[188px] py-4 px-0.5 flex-col gap-1 self-stretch bg-[#ffffff08]">
				<Menu
					selectedKeys={[selectedTab]}
					onSelect={({ key }) => {
						if (key === 'logout') {
							handleLogout();
							return;
						}
						setSelectedTab(key);
					}}
					items={items}
					mode="inline"
					inlineCollapsed={false}
				/>
			</div>
			{selectedTab === 'team' && localStorage.getItem(localStorageKeys.userRole) === userRoles.admin && <Team />}
			{selectedTab === 'language' && <Language />}
			{selectedTab === 'system_config' && <SystemConfig />}
			{selectedTab === 'host' && <Host />}
		</div>
	);
};

export default Settings;
