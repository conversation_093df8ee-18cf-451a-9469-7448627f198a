import React, { useCallback, useEffect, useRef, useState } from 'react'
import { conveyorControllerWidth, conveyorOperation, productDefineLeftNavWidth } from '../../common/const';
import { ConfigProvider } from 'antd';
import { useTranslation } from 'react-i18next';
import { CustomMenu } from '../../common/styledComponent';
import Recipe from './recipe/Index';
import Components from './components/Index';
import _ from 'lodash';
import { useGetProductByIdQuery } from '../../services/product';
import { useSearchParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useSubmitConveyorOperationMutation } from '../../services/conveyor';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';


const Teach = () => {
  const [searchParams] = useSearchParams();

  const productId = searchParams.get('product-id');
  const isFromUpdateCad = searchParams.get('from-upload-cad') === 'true';
  const isFromAutoProgramming = searchParams.get('from-auto-programming') === 'true';
  const inspectables = searchParams.get('inspectables');

  const init = useRef(true);

  const { t } = useTranslation();

  const [isConveyorControllerOpened, setIsConveyorControllerOpened] = useState(false);
  const [selectedTab, setSelectedTab] = useState('recipe'); // recipe, components

  const [recipeActiveTab, setRecipeActiveTab] = useState('PCBDetail');

  const { data: curProduct, isError: isGetCurProdErr, refetch: refetchCurProduct } = useGetProductByIdQuery(productId);
  const [submitConveyorOperation] = useSubmitConveyorOperationMutation();

  const conveyorAccessToken = useSelector((state) => state.setting.conveyorAccessToken);

  const handleSubmitConveyorOperation = useCallback(async (accessToken, operation) => {
    // console.log('accessToken', accessToken);
    // console.log('operation', operation);

    if (_.isEmpty(accessToken)) {
      aoiAlert(t('notification.error.conveyorAccessTokenEmpty'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await submitConveyorOperation({
      conveyor_access_token: accessToken,
      op: operation,
    });

    if (res.error) {
      aoiAlert(t('notification.error.conveyorOperation'), ALERT_TYPES.COMMON_ERROR);
      console.error('submitConveyorOperation error:', _.get(res, 'error.message', ''));
      return;
    }
  }, [submitConveyorOperation, t]);

  useEffect(() => {
    setIsConveyorControllerOpened(selectedTab === 'recipe');
  }, [selectedTab]);

  useEffect(() => {
    refetchCurProduct();

    if (isFromUpdateCad || isFromAutoProgramming) {
      setSelectedTab('components');
    }

    if(!init.current) {
      init.current = true;
    }
  }, []);

  useEffect(() => {
    if (!inspectables || !init.current) {
      return;
    } else if (inspectables === 'true') {
      setSelectedTab('components');
    } else {
      setSelectedTab('recipe');
    }

    init.current = false;
  }, [inspectables, init]);

  return (
    <div className='flex items-center self-stretch flex-1 bg-[#1E1E1E] border-t-[1px] border-t-[#4F4F4F]'>
      {isConveyorControllerOpened && (
        <div
          className={`flex py-4 px-0.5 flex-col self-stretch border-r-[2px] border-r-[#0000001a] bg-[#ffffff08]`}
          style={{
            width: `${conveyorControllerWidth}px`,
          }}
        >
          <ConfigProvider
            theme={{
              components: {
                Menu: {
                  itemHeight: 73,
                }
              }
            }}
          >
            <CustomMenu
              paddingblock={11}
              selectable={false}
              onChange={({ key }) => {
                let operation = '';
                switch (key) {
                  case 'PCBIn':
                    operation = conveyorOperation.load;
                    break;
                  case 'PCBOut':
                    operation = conveyorOperation.unload;
                    break;
                  case 'passThru':
                    operation = conveyorOperation.skip;
                    break;
                  default:
                    return;
                }
                handleSubmitConveyorOperation(conveyorAccessToken, operation);
              }}
              items={[
                {
                  key: 'continuous',
                  label: <div className='flex flex-col justify-center items-center gap-1 self-stretch '>
                    <div className='flex w-8 h-8 justify-center items-center'>
                      <img src='/icn/continuous_white.svg' alt='continuous' className='w-6 h-6 opacity-50' />
                    </div>
                    <span className='font-source text-[10px] font-normal leading-[150%]'>
                      {t('productDefine.continuous')}
                    </span>
                  </div>,
                  disabled: true,
                },
                {
                  key: 'inspect',
                  label: <div className='flex flex-col justify-center items-center gap-1 self-stretch '>
                    <div className='flex w-8 h-8 justify-center items-center'>
                      <img src='/icn/inspect_white.svg' alt='inspect' className='w-6 h-6 opacity-50' />
                    </div>
                    <span className='font-source text-[10px] font-normal leading-[150%]'>
                      {t('productDefine.inspect')}
                    </span>
                  </div>,
                  disabled: true,
                },
                {
                  key: 'stop',
                  label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                    <div className='flex w-8 h-8 justify-center items-center'>
                      <img
                        src='/icn/stop_white.svg'
                        alt='stop'
                        className='w-6 h-6 opacity-50'
                      />
                    </div>
                    <span className='font-source text-[10px] font-normal leading-[150%]'>
                      {t('productDefine.stop')}
                    </span>
                  </div>,
                  disabled: true,
                },
                {
                  key: 'PCBIn',
                  label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                    <div className='flex w-8 h-8 justify-center items-center'>
                      <img
                        src='/icn/pcbIn_color.svg'
                        alt='stop'
                        className='w-6 h-6'
                      />
                    </div>
                    <span className='font-source text-[10px] font-normal leading-[150%]'>
                      {t('productDefine.PCBIn')}
                    </span>
                  </div>,
                  onClick: () => {
                    handleSubmitConveyorOperation(conveyorAccessToken, conveyorOperation.load);
                  },
                  disabled: _.isEmpty(conveyorAccessToken) && recipeActiveTab === 'conveyorSetup',
                },
                {
                  key: 'PCBOut',
                  label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                    <div className={`flex w-8 h-8 justify-center items-center`}>
                      <img
                        src='/icn/pcbOut_color.svg'
                        alt='stop'
                        className={`w-6 h-6 ${(_.isEmpty(conveyorAccessToken) || _.includes(['PCBDimension', 'fullPCBCapture'], recipeActiveTab)) && 'opacity-50'}`}
                      />
                    </div>
                    <span className='font-source text-[10px] font-normal leading-[150%]'>
                      {t('productDefine.PCBEject')}
                    </span>
                  </div>,
                  onClick: () => {
                    handleSubmitConveyorOperation(conveyorAccessToken, conveyorOperation.unload);
                  },
                  disabled: _.isEmpty(conveyorAccessToken) || _.includes(['PCBDimension', 'fullPCBCapture'], recipeActiveTab),
                },
                // {
                //   key: 'clampOn',
                //   label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                //     <div className='flex w-8 h-8 justify-center items-center'>
                //       <img
                //         src='/icn/clampOn_color.svg'
                //         alt='stop'
                //         className='w-6 h-6'
                //       />
                //     </div>
                //     <span className='font-source text-[10px] font-normal leading-[150%]'>
                //       {t('productDefine.clampOn')}
                //     </span>
                //   </div>
                // },
                // {
                //   key: 'clampOff',
                //   label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                //     <div className='flex w-8 h-8 justify-center items-center'>
                //       <img
                //         src='/icn/clampOff_color.svg'
                //         alt='stop'
                //         className='w-6 h-6'
                //       />
                //     </div>
                //     <span className='font-source text-[10px] font-normal leading-[150%]'>
                //       {t('productDefine.clampOff')}
                //     </span>
                //   </div>
                // },
                {
                  key: 'passThru',
                  label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                    <div className='flex w-8 h-8 justify-center items-center'>
                      <img
                        src='/icn/fastForward_white.svg'
                        alt='stop'
                        className='w-6 h-6'
                      />
                    </div>
                    <span className='font-source text-[10px] font-normal leading-[150%]'>
                      {t('productDefine.passThru')}
                    </span>
                  </div>,
                  onClick: () => {
                    handleSubmitConveyorOperation(conveyorAccessToken, conveyorOperation.skip);
                  },
                  disabled: _.isEmpty(conveyorAccessToken),
                },
                {
                  key: 'reset',
                  label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                    <div className='flex w-8 h-8 justify-center items-center'>
                      <img
                        src='/icn/reset_white.svg'
                        alt='stop'
                        className='w-6 h-6 opacity-50'
                      />
                    </div>
                    <span className='font-source text-[10px] font-normal leading-[150%]'>
                      {t('productDefine.reset')}
                    </span>
                  </div>,
                  disabled: _.isEmpty(conveyorAccessToken),
                  // disabled: true,
                },
              ]}
            />
          </ConfigProvider>
        </div>
      )}
      <div
        className={`flex py-4 px-0.5 bg-[#ffffff08] self-stretch`}
        style={{
          width: `${productDefineLeftNavWidth}px`,
        }}
      >
        <CustomMenu
          selectedKeys={[selectedTab]}
          onSelect={({ key }) => setSelectedTab(key)}
          items={[
            {
              key: 'recipe',
              label: <div className='flex items-center w-[120px] gap-2 px-3 self-stretch'>
                <img src='/icn/recipe_white.svg' alt='recipe' className='h-[12px] w-[12px]' />
                <span className={`font-source text-[14px] font-${selectedTab === 'recipe' ? 'semibold' : 'normal'} leading-[normal]`}>
                  {t('productDefine.recipe')}
                </span>
              </div>,
            },
            {
              key: 'components',
              label: <div className='flex items-center w-[120px] gap-2 px-3 self-stretch'>
                <img src='/icn/components_white.svg' alt='recipe' className='h-[12px] w-[12px]' />
                <span className={`font-source text-[14px] font-${selectedTab === 'components' ? 'semibold' : 'normal'} leading-[normal]`}>
                  {t('productDefine.components')}
                </span>
              </div>,
              disabled: _.isEmpty(_.get(curProduct, 'inspectables', []))
            }
          ]}
        />
      </div>
      <div className='flex flex-col flex-1 self-stretch'>
        { selectedTab === 'recipe' && (
          <Recipe
            setIsConveyorControllerOpened={setIsConveyorControllerOpened}
            curProduct={curProduct}
            refetchCurProduct={refetchCurProduct}
            productId={productId}
            activeTab={recipeActiveTab}
            setActiveTab={setRecipeActiveTab}
            setTeachTab={setSelectedTab}
          />
        )}
        { selectedTab === 'components' && (
          <Components
            setIsConveyorControllerOpened={setIsConveyorControllerOpened}
            curProduct={curProduct}refetchCurProduct={refetchCurProduct}
            productId={productId}
            isFromUpdateCad={isFromUpdateCad}
          />
        )}
      </div>
    </div>
  );
};

export default Teach;